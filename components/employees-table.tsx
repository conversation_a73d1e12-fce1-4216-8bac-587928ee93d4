"use client"

import * as React from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type RowSelectionState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown, User, Edit, UserX, UserCheck, Trash2 } from "lucide-react"
import { formatMultiDepartmentDisplay } from "@/lib/utils"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import type { Employee, Department, Manager } from "@/lib/types"
import type { AuthUser } from "@/lib/auth"
import { EmployeeFormDialog } from "./employee-form-dialog"
import { removeEmployeeAction, deleteEmployeeAction } from "@/lib/actions/employees"
import { toast } from "sonner"
import { useIsMobile } from "@/hooks/use-mobile"
import {
  announceToScreenReader,
  getStatusAnnouncement,
  getTableAnnouncement,
  getSortAnnouncement
} from "@/lib/accessibility"
import { getEmployeeDepartmentDisplay } from "@/lib/utils"
import { FixedSizeList as List } from 'react-window'
import { BulkRateTypeDialog } from "./bulk-rate-type-dialog"
import { BulkManagerAssignmentDialog } from "./bulk-manager-assignment-dialog"

export function EmployeesTable({
  data,
  departments,
  managers,
  onRefresh,
  user
}: {
  data: Employee[]
  departments: Department[]
  managers: Manager[]
  onRefresh?: () => void
  user?: AuthUser | null
}) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [isFormOpen, setIsFormOpen] = React.useState(false)
  const [selectedEmployee, setSelectedEmployee] = React.useState<Employee | null>(null)
  const [announceUpdate, setAnnounceUpdate] = React.useState<string>("")
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = React.useState(false)
  const [employeeToRemove, setEmployeeToRemove] = React.useState<Employee | null>(null)
  const [confirmRemoval, setConfirmRemoval] = React.useState(false)
  const [isRemoving, setIsRemoving] = React.useState(false)
  const [isBulkRateDialogOpen, setIsBulkRateDialogOpen] = React.useState(false)
  const [isBulkManagerDialogOpen, setIsBulkManagerDialogOpen] = React.useState(false)
  const [rateFilter, setRateFilter] = React.useState<string>("all")
  const isMobile = useIsMobile()
  
  // Check if user is HR admin or super admin for bulk operations
  const canPerformBulkOperations = user?.role === 'hr-admin' || user?.role === 'super-admin'
  
  // Debug logging (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 [DEBUG] User role:', user?.role)
    console.log('🔍 [DEBUG] Can perform bulk operations:', canPerformBulkOperations)
  }

  // Filter data by rate type
  const filteredData = React.useMemo(() => {
    if (rateFilter === "all") return data
    return data.filter(emp => emp.rate === rateFilter)
  }, [data, rateFilter])

  // Get selected employees for bulk operations
  const selectedEmployees = React.useMemo(() => {
    const selectedIds = new Set(
      Object.keys(rowSelection).filter(key => rowSelection[key])
    )
    return filteredData.filter(row => selectedIds.has(String(row.id)))
  }, [filteredData, rowSelection])

  // Announce table updates to screen readers
  React.useEffect(() => {
    if (announceUpdate) {
      announceToScreenReader(announceUpdate)
      setAnnounceUpdate("")
    }
  }, [announceUpdate])

  // Announce table information on mount
  React.useEffect(() => {
    const tableAnnouncement = getTableAnnouncement(filteredData.length, 5)
    announceToScreenReader(tableAnnouncement)
  }, [filteredData.length])

  // Note: Edit functionality now redirects to profile edit page
  // This function is kept for potential future use but not currently called

  const handleAddNew = () => {
    console.log('[A11Y] Opening new employee dialog')
    setSelectedEmployee(null)
    setIsFormOpen(true)
    setAnnounceUpdate("Opening new employee form")
  }

  const handleFormClose = () => {
    console.log('[A11Y] Closing employee form dialog')
    setIsFormOpen(false)
    setAnnounceUpdate("Employee form closed")
    setTimeout(() => setSelectedEmployee(null), 300)
  }

  const handleDeactivate = async (employee: Employee) => {
    try {
      const result = await deleteEmployeeAction(employee.id)
      if (result.success) {
        toast.success('message' in result ? result.message : 'Employee deactivated successfully')
        onRefresh?.()
      } else {
        toast.error('error' in result ? result.error : 'Failed to deactivate employee')
      }
    } catch (error) {
      toast.error('Failed to deactivate employee')
    }
  }

  const handleRemoveClick = (employee: Employee) => {
    setEmployeeToRemove(employee)
    setIsRemoveDialogOpen(true)
    setConfirmRemoval(false)
  }

  const handleRemoveConfirm = async () => {
    if (!employeeToRemove || !confirmRemoval) return

    setIsRemoving(true)
    try {
      const result = await removeEmployeeAction(employeeToRemove.id)
      if (result.success) {
        toast.success('message' in result ? result.message : 'Employee removed successfully')
        setIsRemoveDialogOpen(false)
        setEmployeeToRemove(null)
        setConfirmRemoval(false)
        onRefresh?.()
      } else {
        toast.error('error' in result ? result.error : 'Failed to remove employee')
      }
    } catch (error) {
      toast.error('Failed to remove employee')
    } finally {
      setIsRemoving(false)
    }
  }

  const handleRemoveCancel = () => {
    setIsRemoveDialogOpen(false)
    setEmployeeToRemove(null)
    setConfirmRemoval(false)
  }

  const handleBulkRateTypeClick = () => {
    setIsBulkRateDialogOpen(true)
  }

  const handleBulkRateTypeSuccess = () => {
    setRowSelection({})
    onRefresh?.()
  }

  const handleBulkManagerClick = () => {
    setIsBulkManagerDialogOpen(true)
  }

  const handleBulkManagerSuccess = () => {
    setRowSelection({})
    onRefresh?.()
  }

  // Build columns array dynamically based on permissions
  const columns: ColumnDef<Employee>[] = React.useMemo(() => {
    const baseColumns: ColumnDef<Employee>[] = []

    // Add selection column for HR admins
    if (canPerformBulkOperations) {
      baseColumns.push({
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all employees"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label={`Select ${row.original.fullName}`}
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      })
    }

    // Add main columns
    baseColumns.push(
      {
        accessorKey: "fullName",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => {
              const newSorting = column.getIsSorted() === "asc" ? "desc" : "asc"
              column.toggleSorting(column.getIsSorted() === "asc")
              setAnnounceUpdate(getSortAnnouncement("Name", newSorting))
            }}
            className="h-auto p-0 font-medium hover:bg-transparent"
            aria-label={getSortAnnouncement("Name", column.getIsSorted() || null)}
          >
            Name
            <ArrowUpDown className="ml-2 h-4 w-4" aria-hidden="true" />
          </Button>
        ),
      },
      {
        accessorKey: "departmentName",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => {
              const newSorting = column.getIsSorted() === "asc" ? "desc" : "asc"
              column.toggleSorting(column.getIsSorted() === "asc")
              setAnnounceUpdate(getSortAnnouncement("Department", newSorting))
            }}
            className="h-auto p-0 font-medium hover:bg-transparent"
            aria-label={getSortAnnouncement("Department", column.getIsSorted() || null)}
          >
            Department
            <ArrowUpDown className="ml-2 h-4 w-4" aria-hidden="true" />
          </Button>
        ),
        cell: ({ row }) => {
          const employee = row.original
          return (
            <span className="text-sm">
              {formatMultiDepartmentDisplay(employee)}
            </span>
          )
        },
      },
      {
        accessorKey: "rate",
        header: "Rate Type",
        cell: ({ row }) => {
          const rate = row.getValue("rate") as "hourly" | "monthly"
          return (
            <Badge 
              variant={rate === "hourly" ? "secondary" : "default"}
              className={rate === "hourly" ? "bg-emerald-100 text-emerald-800 hover:bg-emerald-200" : "bg-blue-100 text-blue-800 hover:bg-blue-200"}
            >
              {rate === "hourly" ? "Hourly" : "Monthly"}
            </Badge>
          )
        },
      },
      {
        accessorKey: "managerName",
        header: "Managers",
        cell: ({ row }) => {
          const employee = row.original

          // Handle multiple managers
          if (employee.managers && employee.managers.length > 0) {
            const primaryManager = employee.managers.find(m => m.isPrimary)
            const otherManagers = employee.managers.filter(m => !m.isPrimary)

            return (
              <div className="space-y-1">
                {primaryManager && (
                  <div className="text-sm font-medium">{primaryManager.managerName}</div>
                )}
                {otherManagers.map((manager) => (
                  <div key={manager.id} className="text-sm text-muted-foreground">
                    {manager.managerName}
                  </div>
                ))}
              </div>
            )
          }

          // Handle legacy single manager
          if (employee.managerName) {
            return <span className="text-sm">{employee.managerName}</span>
          }

          return <span className="text-sm text-muted-foreground">No manager</span>
        },
      },
      {
        accessorKey: "paymentNotes",
        header: "Payment Notes",
        cell: ({ row }) => {
          const paymentNotes = row.getValue("paymentNotes") as string | undefined
          if (!paymentNotes) {
            return <span className="text-muted-foreground text-sm">-</span>
          }
          return (
            <div className="text-sm text-amber-700 font-medium max-w-[200px] truncate" title={paymentNotes}>
              {paymentNotes}
            </div>
          )
        },
      },
    )

    // Add status column (always shown)
    baseColumns.push({
      accessorKey: "active",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.getValue("active")
        const statusText = isActive ? "Active" : "Inactive"
        return (
          <Badge
            variant={isActive ? "default" : "outline"}
            className={isActive ? "bg-green-600" : ""}
            aria-label={getStatusAnnouncement(statusText.toLowerCase())}
          >
            {statusText}
          </Badge>
        )
      },
    })

    // Add actions column (always shown)
    baseColumns.push({
      id: "actions",
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => {
        const employee = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                aria-label={`Actions for ${employee.fullName}`}
              >
                <span className="sr-only">Open menu for {employee.fullName}</span>
                <MoreHorizontal className="h-4 w-4" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions for {employee.fullName}</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${employee.id}/profile`} className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  View Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${employee.id}/profile/edit`} className="flex items-center">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Employee
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDeactivate(employee)}>
                {employee.active ? (
                  <>
                    <UserX className="mr-2 h-4 w-4" />
                    Deactivate Employee
                  </>
                ) : (
                  <>
                    <UserCheck className="mr-2 h-4 w-4" />
                    Activate Employee
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleRemoveClick(employee)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Remove Employee
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    })

    return baseColumns
  }, [setAnnounceUpdate, canPerformBulkOperations])

  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters, rowSelection },
    enableRowSelection: canPerformBulkOperations,
    getRowId: (row) => row.id,
    initialState: {
      pagination: {
        pageSize: 50, // Show 50 employees per page instead of default 10
      },
    },
  })

  // Virtualization threshold - use virtualization for large datasets
  const VIRTUALIZATION_THRESHOLD = 100
  const shouldVirtualize = filteredData.length > VIRTUALIZATION_THRESHOLD

  // Virtualized row component
  const VirtualizedRow = React.memo(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const row = table.getRowModel().rows[index]
    if (!row) return null

    return (
      <div style={style} className="flex border-b">
        {row.getVisibleCells().map((cell) => (
          <div
            key={cell.id}
            className="flex-1 px-4 py-2 text-sm"
            style={{ minWidth: '120px' }}
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </div>
        ))}
      </div>
    )
  })

  // Mobile card component for responsive design
  const MobileEmployeeCard = ({ employee }: { employee: Employee }) => (
    <Card key={employee.id} className="mb-3">
      <CardHeader className="pb-2 px-4 pt-4">
        <div className="flex items-start justify-between gap-3">
          <div className="space-y-1 flex-1 min-w-0">
            <h3 className="text-base font-semibold leading-tight truncate">{employee.fullName}</h3>
            <p className="text-sm text-muted-foreground truncate">
              {getEmployeeDepartmentDisplay(employee)}
            </p>
            <div className="flex items-center gap-2">
              <Badge 
                variant={employee.rate === "hourly" ? "secondary" : "default"}
                className={employee.rate === "hourly" ? "bg-emerald-100 text-emerald-800 hover:bg-emerald-200" : "bg-blue-100 text-blue-800 hover:bg-blue-200"}
              >
                {employee.rate === "hourly" ? "Hourly" : "Monthly"}
              </Badge>
              <Badge variant={employee.active ? "default" : "outline"} className={employee.active ? "bg-green-600" : ""}>
                {employee.active ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 w-9 p-0 flex-shrink-0 touch-manipulation"
                aria-label={`Actions for ${employee.fullName}`}
              >
                <span className="sr-only">Open menu for {employee.fullName}</span>
                <MoreHorizontal className="h-4 w-4" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Actions for {employee.fullName}</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${employee.id}/profile`} className="flex items-center touch-target">
                  <User className="mr-2 h-4 w-4" />
                  View Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${employee.id}/profile/edit`} className="flex items-center touch-target">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Employee
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDeactivate(employee)}>
                {employee.active ? (
                  <>
                    <UserX className="mr-2 h-4 w-4" />
                    Deactivate Employee
                  </>
                ) : (
                  <>
                    <UserCheck className="mr-2 h-4 w-4" />
                    Activate Employee
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleRemoveClick(employee)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Remove Employee
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-4 pt-0">
        <div className="space-y-2">
          {/* Managers */}
          {(employee.managers?.length || employee.managerName) && (
            <div>
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Managers</span>
              <div className="mt-1">
                {employee.managers && employee.managers.length > 0 ? (
                  employee.managers.map((manager) => (
                    <div key={manager.id} className={manager.isPrimary ? "text-sm font-medium" : "text-sm text-muted-foreground"}>
                      {manager.managerName}
                    </div>
                  ))
                ) : employee.managerName ? (
                  <span className="text-sm">{employee.managerName}</span>
                ) : null}
              </div>
            </div>
          )}

          {/* Payment Notes */}
          {employee.paymentNotes && (
            <div>
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Payment Notes</span>
              <div className="mt-1 text-sm text-amber-700 font-medium">
                {employee.paymentNotes}
              </div>
            </div>
          )}

          {/* Status */}
          <div className="flex items-center justify-between pt-1">
            <span className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</span>
            <Badge
              variant={employee.active ? "default" : "outline"}
              className={`text-xs ${employee.active ? "bg-green-600" : ""}`}
              aria-label={getStatusAnnouncement(employee.active ? 'active' : 'inactive')}
            >
              {employee.active ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div>
      {/* Header with search and add button */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 py-4">
        <div className="flex flex-1 gap-2">
          <Input
            placeholder="Filter by name..."
            value={(table.getColumn("fullName")?.getFilterValue() as string) ?? ""}
            onChange={(event) => {
              const value = event.target.value
              table.getColumn("fullName")?.setFilterValue(value)
              if (value) {
                setAnnounceUpdate(`Filtering employees by name: ${value}`)
              } else {
                setAnnounceUpdate("Filter cleared, showing all employees")
              }
            }}
            className="flex-1"
            aria-label="Filter employees by name"
          />
          <Select value={rateFilter} onValueChange={setRateFilter}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="hourly">Hourly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {(user?.role === 'super-admin' || user?.role === 'hr-admin') && (
          <Button onClick={handleAddNew} className="w-full sm:w-auto flex-shrink-0">
            Add New Employee
          </Button>
        )}
      </div>

      {/* Bulk actions toolbar for HR admins */}
      {canPerformBulkOperations && selectedEmployees.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-md p-4 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                {selectedEmployees.length} employee{selectedEmployees.length === 1 ? '' : 's'} selected
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setRowSelection({})}>
                Clear Selection
              </Button>
              <Button variant="default" size="sm" onClick={handleBulkRateTypeClick}>
                Change Rate Type
              </Button>
              <Button variant="outline" size="sm" onClick={handleBulkManagerClick}>
                Assign Managers
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Mobile card view */}
      {isMobile ? (
        <div className="space-y-4">
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <MobileEmployeeCard key={row.id} employee={row.original} />
            ))
          ) : (
            <Card>
              <CardContent className="py-8">
                <p className="text-center text-muted-foreground">No employees found.</p>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        /* Desktop table view */
        <div className="rounded-md border">
          <Table
            aria-label={getTableAnnouncement(data.length, 5)}
            role="table"
          >
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} role="row">
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id} role="columnheader">
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            {shouldVirtualize ? (
              /* Virtualized table body for large datasets */
              <tbody>
                <tr>
                  <td colSpan={columns.length} style={{ padding: 0 }}>
                    {table.getRowModel().rows?.length ? (
                      <div className="relative">
                        <div className="text-xs text-muted-foreground p-2 bg-muted/50 border-b">
                          Showing {table.getRowModel().rows.length} employees (virtualized for performance)
                        </div>
                        <List
                          height={400} // Fixed height for virtualization
                          itemCount={table.getRowModel().rows.length}
                          itemSize={60} // Height of each row
                          width="100%"
                        >
                          {VirtualizedRow}
                        </List>
                      </div>
                    ) : (
                      <div className="h-24 flex items-center justify-center text-center text-muted-foreground">
                        No results.
                      </div>
                    )}
                  </td>
                </tr>
              </tbody>
            ) : (
              /* Standard table body for smaller datasets */
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row, index) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      role="row"
                      aria-rowindex={index + 2} // +2 because header is row 1
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          role="gridcell"
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow role="row">
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                      role="gridcell"
                      aria-label="No employees found"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            )}
          </Table>
        </div>
      )}

      {/* Pagination */}
      <nav className="flex items-center justify-center sm:justify-end space-x-2 py-4" aria-label="Table pagination">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            table.previousPage()
            setAnnounceUpdate("Moved to previous page")
          }}
          disabled={!table.getCanPreviousPage()}
          aria-label="Go to previous page"
          className="min-h-[44px] px-4"
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            table.nextPage()
            setAnnounceUpdate("Moved to next page")
          }}
          disabled={!table.getCanNextPage()}
          aria-label="Go to next page"
          className="min-h-[44px] px-4"
        >
          Next
        </Button>
      </nav>
      <EmployeeFormDialog
        isOpen={isFormOpen}
        onClose={handleFormClose}
        employee={selectedEmployee}
        departments={departments}
        managers={managers}
        user={user}
      />

      {/* Remove Employee Confirmation Dialog */}
      <Dialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Employee</DialogTitle>
            <DialogDescription>
              Are you sure you want to permanently remove <strong>{employeeToRemove?.fullName}</strong> from the system?
              <br /><br />
              <span className="text-red-600 font-medium">
                ⚠️ This action cannot be undone. All employee data, appraisals, and related records will be permanently deleted.
              </span>
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center space-x-2 py-4">
            <Checkbox
              id="confirm-removal"
              checked={confirmRemoval}
              onCheckedChange={(checked) => setConfirmRemoval(checked === true)}
            />
            <label
              htmlFor="confirm-removal"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Yes, I understand this action cannot be undone
            </label>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleRemoveCancel}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRemoveConfirm}
              disabled={!confirmRemoval || isRemoving}
            >
              {isRemoving ? "Removing..." : "Remove Employee"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Rate Type Dialog */}
      <BulkRateTypeDialog
        isOpen={isBulkRateDialogOpen}
        onClose={() => setIsBulkRateDialogOpen(false)}
        selectedEmployees={selectedEmployees}
        onSuccess={handleBulkRateTypeSuccess}
      />

      {/* Bulk Manager Assignment Dialog */}
      <BulkManagerAssignmentDialog
        isOpen={isBulkManagerDialogOpen}
        onClose={() => setIsBulkManagerDialogOpen(false)}
        selectedEmployees={selectedEmployees}
        managers={managers}
        onSuccess={handleBulkManagerSuccess}
      />
    </div>
  )
}
