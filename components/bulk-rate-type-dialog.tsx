"use client"

import { useState } from "react"
import { useTransition } from "react"
import { <PERSON>, Lo<PERSON>, <PERSON>ert<PERSON>riangle } from "lucide-react"
import { toast } from "sonner"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import type { Employee } from "@/lib/types"

interface BulkRateTypeDialogProps {
  isOpen: boolean
  onClose: () => void
  selectedEmployees: Employee[]
  onSuccess?: () => void
}

export function BulkRateTypeDialog({
  isOpen,
  onClose,
  selectedEmployees,
  onSuccess
}: BulkRateTypeDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [newRateType, setNewRateType] = useState<"hourly" | "monthly" | "">("")

  // Group employees by current rate type
  const hourlyEmployees = selectedEmployees.filter(emp => emp.rate === "hourly")
  const monthlyEmployees = selectedEmployees.filter(emp => emp.rate === "monthly")

  const handleSubmit = () => {
    if (!newRateType) {
      setSubmitError("Please select a rate type")
      return
    }

    setSubmitError(null)

    startTransition(async () => {
      try {
        // Import the action dynamically to avoid circular dependencies
        const { bulkUpdateRateType } = await import("@/lib/actions/bulk-employees")
        
        const employeeIds = selectedEmployees.map(emp => emp.id)
        const result = await bulkUpdateRateType(employeeIds, newRateType)

        if (result.success) {
          toast.success(
            `Successfully updated rate type to ${newRateType} for ${selectedEmployees.length} employee${selectedEmployees.length === 1 ? '' : 's'}`
          )
          onSuccess?.()
          onClose()
        } else {
          setSubmitError(result.error || "Failed to update rate types")
        }
      } catch (error) {
        console.error("Error updating rate types:", error)
        setSubmitError("An unexpected error occurred")
      }
    })
  }

  const handleClose = () => {
    if (!isPending) {
      setNewRateType("")
      setSubmitError(null)
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Change Rate Type
          </DialogTitle>
          <DialogDescription>
            Update the rate type for {selectedEmployees.length} selected employee{selectedEmployees.length === 1 ? '' : 's'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current distribution */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Current Rate Types:</h4>
            <div className="flex flex-wrap gap-2">
              {hourlyEmployees.length > 0 && (
                <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">
                  {hourlyEmployees.length} Hourly
                </Badge>
              )}
              {monthlyEmployees.length > 0 && (
                <Badge variant="default" className="bg-blue-100 text-blue-800">
                  {monthlyEmployees.length} Monthly
                </Badge>
              )}
            </div>
          </div>

          {/* Rate type selector */}
          <div className="space-y-2">
            <label className="text-sm font-medium">New Rate Type:</label>
            <Select value={newRateType} onValueChange={(value: "hourly" | "monthly") => setNewRateType(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select rate type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hourly">Hourly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Preview of changes */}
          {newRateType && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Changes Preview:</h4>
              <div className="max-h-32 overflow-y-auto bg-gray-50 dark:bg-gray-900 rounded-md p-3">
                <div className="space-y-1 text-sm">
                  {selectedEmployees
                    .filter(emp => emp.rate !== newRateType)
                    .map(emp => (
                      <div key={emp.id} className="flex items-center justify-between">
                        <span>{emp.fullName}</span>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant="outline" 
                            className="text-xs"
                          >
                            {emp.rate === "hourly" ? "Hourly" : "Monthly"}
                          </Badge>
                          <span className="text-xs">→</span>
                          <Badge 
                            variant={newRateType === "hourly" ? "secondary" : "default"}
                            className={`text-xs ${newRateType === "hourly" ? "bg-emerald-100 text-emerald-800" : "bg-blue-100 text-blue-800"}`}
                          >
                            {newRateType === "hourly" ? "Hourly" : "Monthly"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedEmployees.filter(emp => emp.rate !== newRateType).length} employee{selectedEmployees.filter(emp => emp.rate !== newRateType).length === 1 ? '' : 's'} will be updated.
                {selectedEmployees.filter(emp => emp.rate === newRateType).length > 0 && (
                  ` ${selectedEmployees.filter(emp => emp.rate === newRateType).length} already have this rate type.`
                )}
              </p>
            </div>
          )}

          {submitError && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isPending}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isPending || !newRateType}>
            {isPending ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              `Update ${selectedEmployees.filter(emp => emp.rate !== newRateType).length} Employee${selectedEmployees.filter(emp => emp.rate !== newRateType).length === 1 ? '' : 's'}`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}