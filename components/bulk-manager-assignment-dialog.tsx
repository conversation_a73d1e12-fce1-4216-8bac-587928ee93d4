"use client"

import { useState } from "react"
import { useTransition } from "react"
import { <PERSON>, Lo<PERSON>, <PERSON>ert<PERSON>riangle } from "lucide-react"
import { toast } from "sonner"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { MultiManagerSelector } from "./multi-manager-selector"
import type { Employee, Manager } from "@/lib/types"

interface SelectedManager {
  id: string
  name: string
  isPrimary: boolean
}

interface BulkManagerAssignmentDialogProps {
  isOpen: boolean
  onClose: () => void
  selectedEmployees: Employee[]
  managers: Manager[]
  onSuccess?: () => void
}

export function BulkManagerAssignmentDialog({
  isOpen,
  onClose,
  selectedEmployees,
  managers,
  onSuccess
}: BulkManagerAssignmentDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [selectedManagers, setSelectedManagers] = useState<SelectedManager[]>([])

  const handleSubmit = () => {
    if (selectedEmployees.length === 0) {
      setSubmitError("Please select at least one employee")
      return
    }
    
    if (selectedManagers.length === 0) {
      setSubmitError("Please select at least one manager")
      return
    }

    setSubmitError(null)

    startTransition(async () => {
      try {
        // Import the action dynamically to avoid circular dependencies
        const { bulkAssignManagers } = await import("@/lib/actions/bulk-employees")
        
        const employeeIds = selectedEmployees.map(emp => emp.id)
        const managerIds = selectedManagers.map(mgr => mgr.id)
        const primaryManagerId = selectedManagers.find(mgr => mgr.isPrimary)?.id

        const result = await bulkAssignManagers(employeeIds, managerIds, primaryManagerId)

        if (result.success) {
          toast.success(
            `Successfully assigned ${selectedManagers.length} manager${selectedManagers.length === 1 ? '' : 's'} to ${selectedEmployees.length} employee${selectedEmployees.length === 1 ? '' : 's'}`
          )
          onSuccess?.()
          onClose()
        } else {
          setSubmitError(result.error || "Failed to assign managers")
        }
      } catch (error) {
        console.error("Error assigning managers:", error)
        setSubmitError("An unexpected error occurred")
      }
    })
  }

  const handleOpenChange = (open: boolean) => {
    if (!open && !isPending) {
      setSelectedManagers([])
      setSubmitError(null)
      onClose()
    }
  }

  const handleClose = () => {
    if (!isPending) {
      setSelectedManagers([])
      setSubmitError(null)
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Assign Managers
          </DialogTitle>
          <DialogDescription>
            Assign managers to {selectedEmployees.length} selected employee{selectedEmployees.length === 1 ? '' : 's'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Selected employees list */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Selected Employees ({selectedEmployees.length}):</h4>
            <div className="max-h-32 overflow-y-auto bg-gray-50 dark:bg-gray-900 rounded-md p-3">
              <div className="space-y-1 text-sm">
                {selectedEmployees.map(emp => (
                  <div key={emp.id} className="flex items-center justify-between">
                    <span>{emp.fullName}</span>
                    <span className="text-xs text-muted-foreground">{emp.departmentName}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Manager selector */}
          <div className="space-y-2">
            <div className="space-y-1">
              <label className="text-sm font-medium">
                Assign Managers
              </label>
              <p className="text-xs text-muted-foreground">
                Select managers to assign to all selected employees. The first manager selected will be the primary manager.
              </p>
            </div>
            <MultiManagerSelector
              managers={managers}
              selectedManagers={selectedManagers}
              onManagersChange={setSelectedManagers}
              disabled={isPending}
              placeholder="Select managers to assign..."
            />
          </div>

          {/* Preview of assignment */}
          {selectedManagers.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Assignment Preview:</h4>
              <div className="bg-blue-50 dark:bg-blue-950/30 rounded-md p-3">
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Managers to assign:</span>
                    {selectedManagers.map((manager, index) => (
                      <span
                        key={manager.id}
                        className={`px-2 py-1 rounded text-xs ${
                          manager.isPrimary 
                            ? "bg-blue-100 text-blue-800 font-medium" 
                            : "bg-gray-100 text-gray-700"
                        }`}
                      >
                        {manager.name} {manager.isPrimary && "(Primary)"}
                      </span>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    This will replace existing manager assignments for all selected employees.
                  </p>
                </div>
              </div>
            </div>
          )}

          {submitError && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isPending}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={selectedEmployees.length === 0 || selectedManagers.length === 0 || isPending}>
            {isPending ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                Assigning...
              </>
            ) : (
              `Assign Managers to ${selectedEmployees.length} Employee${selectedEmployees.length === 1 ? '' : 's'}`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}