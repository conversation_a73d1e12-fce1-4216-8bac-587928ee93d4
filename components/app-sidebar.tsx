"use client"

import { useState, useEffect } from "react"
import { Briefcase, Building2, ChevronUp, ChevronDown, Home, User, Users, CalendarClock, Shield, UserPlus, Star, LogOut, Calendar, Calculator, MessageSquare, UserCheck, Mail, GitBranch } from "lucide-react"
import Link from "next/link"
import { useClerk } from "@clerk/nextjs"
import { debug } from "@/lib/debug"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from "@/components/ui/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import type { MockUser } from "@/lib/types"
import type { User<PERSON><PERSON> } from "@/lib/schemas"

// Define navigation items with role-based access control
interface NavItem {
  href: string
  label: string
  icon: any
  roles: UserRole[]
  description?: string
  requiresMarketingDept?: boolean
}

// Define navigation groups for organized sidebar
interface NavGroup {
  id: string
  label: string
  icon: any
  items: NavItem[]
  roles: UserRole[]
  collapsible?: boolean
}

// Single navigation items (no grouping needed)
const singleNavItems: NavItem[] = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: Home,
    roles: ["manager", "senior-manager", "accountant", "hr-admin", "admin", "super-admin"],
    description: "Overview and quick actions"
  },
  {
    href: "/dashboard/accounting",
    label: "Accounting",
    icon: Calculator,
    roles: ["accountant", "super-admin"],
    description: "Manage employee payments and export payroll data"
  },
  {
    href: "/dashboard/pto",
    label: "PTO",
    icon: Calendar,
    roles: ["manager", "senior-manager", "accountant", "hr-admin", "admin", "super-admin"],
    description: "Manage time off requests and approvals"
  }
]

// Grouped navigation items (collapsible groups)
const navigationGroups: NavGroup[] = [
  {
    id: "people",
    label: "People Management",
    icon: Users,
    roles: ["manager", "senior-manager", "accountant", "hr-admin", "admin", "super-admin"],
    collapsible: true,
    items: [
      {
        href: "/dashboard/employees",
        label: "Employees",
        icon: Users,
        roles: ["manager", "senior-manager", "hr-admin", "admin", "super-admin"],
        description: "Manage employee records"
      },
      {
        href: "/dashboard/employees/hierarchy",
        label: "Hierarchy",
        icon: GitBranch,
        roles: ["manager", "senior-manager", "accountant", "hr-admin", "admin", "super-admin"],
        description: "View organizational hierarchy"
      },
      {
        href: "/dashboard/add-people",
        label: "Add People",
        icon: UserPlus,
        roles: ["hr-admin", "admin", "super-admin"],
        description: "Add new employees, managers, and departments"
      },
      {
        href: "/dashboard/departments",
        label: "Departments",
        icon: Building2,
        roles: ["hr-admin", "admin", "super-admin"],
        description: "Manage departments"
      }
    ]
  },
  {
    id: "appraisals",
    label: "Appraisal System",
    icon: Shield,
    roles: ["manager", "senior-manager", "hr-admin", "admin", "super-admin"],
    collapsible: true,
    items: [
      {
        href: "/dashboard/team",
        label: "My Appraisals",
        icon: Star,
        roles: ["manager", "senior-manager", "hr-admin", "admin", "super-admin"],
        description: "Create and manage team member appraisals"
      },
      {
        href: "/dashboard/approvals/multi-level",
        label: "Approvals",
        icon: UserCheck,
        roles: ["manager", "senior-manager", "hr-admin", "admin", "super-admin"],
        description: "Review and approve submitted appraisals with multi-level workflow"
      },
      {
        href: "/dashboard/periods",
        label: "Appraisal Periods",
        icon: CalendarClock,
        roles: ["hr-admin", "admin", "super-admin"],
        description: "Manage appraisal periods"
      }
    ]
  },
  {
    id: "communication",
    label: "Communication",
    icon: MessageSquare,
    roles: ["manager", "senior-manager", "accountant", "hr-admin", "admin", "super-admin"],
    collapsible: true,
    items: [
      {
        href: "/dashboard/feedback",
        label: "Feedback",
        icon: MessageSquare,
        roles: ["manager", "senior-manager", "accountant", "hr-admin", "admin", "super-admin"],
        description: "Submit feedback and suggestions to HR"
      },
      {
        href: "/dashboard/hr/feedback",
        label: "HR Feedback",
        icon: MessageSquare,
        roles: ["hr-admin", "super-admin"],
        description: "Manage employee feedback and complaints"
      }
    ]
  },
  {
    id: "administration",
    label: "Administration",
    icon: Shield,
    roles: ["super-admin"],
    collapsible: true,
    items: [
      {
        href: "/dashboard/admin",
        label: "System Admin",
        icon: Shield,
        roles: ["super-admin"],
        description: "System administration"
      },
      {
        href: "/dashboard/admin/email-settings",
        label: "Email Settings",
        icon: Mail,
        roles: ["super-admin"],
        description: "Configure email templates and SMTP settings"
      }
    ]
  }
]



function hasAccess(userRole: UserRole, allowedRoles: UserRole[]): boolean {
  const hasAccess = allowedRoles.includes(userRole)
  debug.log('🔍 [SIDEBAR] hasAccess check:', {
    userRole,
    allowedRoles,
    hasAccess
  })
  return hasAccess
}

function filterGroupItems(group: NavGroup, user: MockUser): NavItem[] {
  return group.items.filter(item => {
    const hasRoleAccess = hasAccess(user.role as UserRole, item.roles)

    // Debug logging for System Admin item specifically
    if (item.label === 'System Admin') {
      debug.log('🔍 [SIDEBAR] System Admin item check:', {
        userRole: user.role,
        requiredRoles: item.roles,
        hasAccess: hasRoleAccess
      })
    }

    // For PTO, only show to Marketing department users or super-admins
    if (item.requiresMarketingDept) {
      const isSuperAdmin = user.role === 'super-admin'

      // Super-admins have unrestricted access to all menu items
      if (isSuperAdmin) {
        return hasRoleAccess
      }

      const isMarketingUser = user.fullName?.toLowerCase().includes('natalie') ||
                              user.id === 'user_natalie'
      return hasRoleAccess && isMarketingUser
    }

    return hasRoleAccess
  })
}

function getRoleBadgeColor(role: UserRole): string {
  switch (role) {
    case 'super-admin': return 'bg-red-100 text-red-800'
    case 'hr-admin': return 'bg-blue-100 text-blue-800'
    case 'senior-manager': return 'bg-teal-100 text-teal-800'
    case 'admin': return 'bg-orange-100 text-orange-800'
    case 'accountant': return 'bg-green-100 text-green-800'
    case 'manager': return 'bg-purple-100 text-purple-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export function AppSidebar({ user }: { user: MockUser }) {
  const { signOut } = useClerk()

  // State for collapsed groups
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(new Set())

  // Load collapsed state from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('sidebar-collapsed-groups')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setCollapsedGroups(new Set(parsed))
      } catch (error) {
        debug.log('🔍 [SIDEBAR] Failed to parse saved collapsed groups:', error)
      }
    }
  }, [])

  // Save collapsed state to localStorage
  const toggleGroupCollapse = (groupId: string) => {
    setCollapsedGroups(prev => {
      const newSet = new Set(prev)
      if (newSet.has(groupId)) {
        newSet.delete(groupId)
      } else {
        newSet.add(groupId)
      }

      // Save to localStorage
      localStorage.setItem('sidebar-collapsed-groups', JSON.stringify([...newSet]))

      debug.log('🔍 [SIDEBAR] Toggled group collapse:', {
        groupId,
        isCollapsed: newSet.has(groupId),
        allCollapsed: [...newSet]
      })

      return newSet
    })
  }

  // Debug logging
  debug.log('🔍 [SIDEBAR] User data received:', user)
  debug.log('🔍 [SIDEBAR] User role:', user.role)
  debug.log('🔍 [SIDEBAR] Is super-admin?', user.role === 'super-admin')

  // Filter single navigation items
  const accessibleSingleItems = singleNavItems.filter(item => {
    const hasRoleAccess = hasAccess(user.role as UserRole, item.roles)

    // For PTO, only show to Marketing department users or super-admins
    if (item.requiresMarketingDept) {
      const isSuperAdmin = user.role === 'super-admin'

      if (isSuperAdmin) {
        return hasRoleAccess
      }

      const isMarketingUser = user.fullName?.toLowerCase().includes('natalie') ||
                              user.id === 'user_natalie'
      return hasRoleAccess && isMarketingUser
    }

    return hasRoleAccess
  })

  // Filter navigation groups and items based on user role and department
  const accessibleGroups = navigationGroups.map(group => {
    // Filter items within the group using helper function
    const accessibleItems = filterGroupItems(group, user)

    // Only return group if it has accessible items and user has access to the group
    if (accessibleItems.length > 0 && hasAccess(user.role as UserRole, group.roles)) {
      debug.log('🔍 [SIDEBAR] Group accessible:', {
        groupId: group.id,
        groupLabel: group.label,
        itemCount: accessibleItems.length,
        userRole: user.role
      })

      return {
        ...group,
        items: accessibleItems
      }
    }

    debug.log('🔍 [SIDEBAR] Group filtered out:', {
      groupId: group.id,
      groupLabel: group.label,
      hasItems: accessibleItems.length > 0,
      hasGroupAccess: hasAccess(user.role as UserRole, group.roles),
      userRole: user.role
    })

    return null
  }).filter(Boolean) as NavGroup[]

  // Add user's own admin page only for regular admins (not super-admins)
  if (user.role === 'admin') {
    // Find or create administration group for regular admins
    let adminGroup = accessibleGroups.find(group => group.id === 'administration')

    const adminItem = {
      href: `/dashboard/admin/${user.id}`,
      label: "Admin Panel",
      icon: Shield,
      roles: ["admin"] as UserRole[],
      description: "Your admin dashboard"
    }

    if (adminGroup) {
      adminGroup.items.push(adminItem)
    } else {
      // Create new admin group for regular admins
      accessibleGroups.push({
        id: 'administration',
        label: 'Administration',
        icon: Shield,
        roles: ['admin'],
        collapsible: true,
        items: [adminItem]
      })
    }
  }

  const handleSignOut = () => {
    signOut()
  }

  return (
    <Sidebar id="navigation" role="navigation" aria-label="Main navigation">
      <SidebarHeader>
        <div className="flex items-center gap-3 p-2">
          <Briefcase className="h-6 w-6 text-primary-foreground flex-shrink-0" aria-hidden="true" />
          <span className="text-lg font-semibold text-primary-foreground group-data-[collapsible=icon]:hidden">
            AppraisalTool
          </span>
        </div>
      </SidebarHeader>
      <SidebarContent>
        {/* Single navigation items (no grouping) */}
        <SidebarMenu role="list">
          {accessibleSingleItems.map((item, index) => (
            <SidebarMenuItem key={`${item.href}-${index}`} role="listitem">
              <SidebarMenuButton
                asChild
                tooltip={item.description || item.label}
                className="min-h-[44px] min-w-[44px] touch-manipulation"
              >
                <Link
                  href={item.href}
                  aria-label={`${item.label}: ${item.description}`}
                  className="flex items-center gap-3 px-3 py-2 rounded-md transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus:bg-sidebar-accent focus:text-sidebar-accent-foreground focus:outline-none focus:ring-2 focus:ring-sidebar-ring"
                >
                  <item.icon aria-hidden="true" className="h-5 w-5 flex-shrink-0" />
                  <span className="truncate">{item.label}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>

        {/* Collapsible groups */}
        {accessibleGroups.map((group) => {
          const isCollapsed = collapsedGroups.has(group.id)

          return (
            <SidebarGroup key={group.id}>
              <SidebarGroupLabel
                className="flex items-center gap-2 cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground rounded-md px-2 py-1 transition-colors min-h-[44px] touch-manipulation"
                onClick={() => toggleGroupCollapse(group.id)}
                role="button"
                aria-expanded={!isCollapsed}
                aria-controls={`group-${group.id}-content`}
              >
                <group.icon className="h-4 w-4" aria-hidden="true" />
                <span className="flex-1">{group.label}</span>
                {isCollapsed ? (
                  <ChevronDown className="h-4 w-4 transition-transform" aria-hidden="true" />
                ) : (
                  <ChevronUp className="h-4 w-4 transition-transform" aria-hidden="true" />
                )}
              </SidebarGroupLabel>
              {!isCollapsed && (
                <SidebarGroupContent id={`group-${group.id}-content`}>
                  <SidebarMenu role="list">
                    {group.items.map((item, index) => (
                      <SidebarMenuItem key={`${item.href}-${index}`} role="listitem">
                        <SidebarMenuButton
                          asChild
                          tooltip={item.description || item.label}
                          className="min-h-[44px] min-w-[44px] touch-manipulation"
                        >
                          <Link
                            href={item.href}
                            aria-label={`${item.label}: ${item.description}`}
                            className="flex items-center gap-3 px-3 py-2 rounded-md transition-colors hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus:bg-sidebar-accent focus:text-sidebar-accent-foreground focus:outline-none focus:ring-2 focus:ring-sidebar-ring"
                          >
                            <item.icon aria-hidden="true" className="h-5 w-5 flex-shrink-0" />
                            <span className="truncate">{item.label}</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              )}
            </SidebarGroup>
          )
        })}
      </SidebarContent>
      <SidebarSeparator />
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton tooltip={`${user.fullName} (${user.role})`}>
                  <User />
                  <div className="flex flex-col items-start">
                    <span className="text-sm font-medium">{user.fullName}</span>
                    <Badge
                      variant="secondary"
                      className={`text-xs ${getRoleBadgeColor(user.role as UserRole)}`}
                    >
                      {user.role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </div>
                  <ChevronUp className="ml-auto" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent side="top" align="start" className="w-56">
                <DropdownMenuLabel>
                  <div className="flex flex-col space-y-1">
                    <span>{user.fullName}</span>
                    <span className="text-xs text-muted-foreground">
                      {user.role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  className="text-red-600 cursor-pointer"
                  onClick={handleSignOut}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}
