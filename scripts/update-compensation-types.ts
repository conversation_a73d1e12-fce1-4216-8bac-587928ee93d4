#!/usr/bin/env tsx

import fs from 'fs'
import path from 'path'
import { supabaseAdmin } from '../lib/supabase-admin'

interface CSVEmployee {
  name: string
  type: string
}

interface DatabaseEmployee {
  id: string
  full_name: string
  rate: 'hourly' | 'monthly'
  active: boolean
}

interface UpdateResult {
  updated: string[]
  failed: string[]
  specialCases: { name: string; type: string }[]
  alreadyCorrect: string[]
}

class CompensationTypeUpdater {
  private csvData: CSVEmployee[] = []
  private dbEmployees: DatabaseEmployee[] = []
  private dryRun: boolean = false

  constructor(dryRun: boolean = true) {
    this.dryRun = dryRun
    console.log(`🔧 Running in ${dryRun ? 'DRY RUN' : 'EXECUTE'} mode`)
  }

  /**
   * Parse CSV file and extract employee compensation data
   */
  private parseCsv(): void {
    console.log('📄 Parsing CSV file...')
    const csvPath = path.join(process.cwd(), 'docs', 'Performance Appraisal - Type.csv')
    
    if (!fs.existsSync(csvPath)) {
      throw new Error(`CSV file not found at: ${csvPath}`)
    }

    const csvContent = fs.readFileSync(csvPath, 'utf-8')
    const lines = csvContent.trim().split('\n').slice(1) // Skip header

    this.csvData = lines.map(line => {
      // Handle commas in names by splitting on last comma
      const lastCommaIndex = line.lastIndexOf(',')
      const name = line.substring(0, lastCommaIndex).trim().replace(/"/g, '')
      const type = line.substring(lastCommaIndex + 1).trim()
      
      return { name, type }
    })

    console.log(`✅ Parsed ${this.csvData.length} employees from CSV`)
  }

  /**
   * Fetch all employees from database
   */
  private async fetchDatabaseEmployees(): Promise<void> {
    console.log('🗄️ Fetching employees from database...')
    
    const { data, error } = await supabaseAdmin
      .from('appy_employees')
      .select('id, full_name, rate, active')
      .eq('active', true)

    if (error) {
      throw new Error(`Database error: ${error.message}`)
    }

    this.dbEmployees = data || []
    console.log(`✅ Fetched ${this.dbEmployees.length} active employees from database`)
  }

  /**
   * Normalize name for matching (remove extra spaces, handle common variations)
   */
  private normalizeName(name: string): string {
    return name
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '')
      .trim()
  }

  /**
   * Find database employee by name (with fuzzy matching)
   */
  private findEmployeeByName(csvName: string): DatabaseEmployee | null {
    const normalizedCsvName = this.normalizeName(csvName)
    
    // Direct match first
    let match = this.dbEmployees.find(emp => 
      this.normalizeName(emp.full_name) === normalizedCsvName
    )
    
    if (match) return match

    // Handle specific name variations
    const nameVariations = {
      'francesco paolo oddo': 'francesco oddo',
      'nato saginashvili': 'natalie saginashvili',
      'tracy rahme': 'tracy rahmeh',
      'joe sayegh': 'joe s',
      'vanessa carolina rojas vergara': 'vanessa vergara',
      'angeles jose ryan iii': 'jose ryan iii angeles'
    }

    const variation = nameVariations[normalizedCsvName as keyof typeof nameVariations]
    if (variation) {
      match = this.dbEmployees.find(emp => 
        this.normalizeName(emp.full_name) === variation
      )
      if (match) return match
    }

    // Partial match (last resort)
    const csvWords = normalizedCsvName.split(' ')
    match = this.dbEmployees.find(emp => {
      const dbWords = this.normalizeName(emp.full_name).split(' ')
      const commonWords = csvWords.filter(word => dbWords.includes(word))
      return commonWords.length >= Math.min(2, csvWords.length)
    })

    return match || null
  }

  /**
   * Categorize employees by compensation type
   */
  private categorizeEmployees(): {
    hourly: DatabaseEmployee[]
    monthly: DatabaseEmployee[]
    specialCases: { csv: CSVEmployee; db?: DatabaseEmployee }[]
    notFound: CSVEmployee[]
  } {
    console.log('🔍 Categorizing employees...')

    const hourly: DatabaseEmployee[] = []
    const monthly: DatabaseEmployee[] = []
    const specialCases: { csv: CSVEmployee; db?: DatabaseEmployee }[] = []
    const notFound: CSVEmployee[] = []

    for (const csvEmp of this.csvData) {
      const dbEmp = this.findEmployeeByName(csvEmp.name)
      
      if (!dbEmp) {
        notFound.push(csvEmp)
        continue
      }

      const type = csvEmp.type.toLowerCase().trim()

      if (type === 'hourly') {
        hourly.push(dbEmp)
      } else if (type === 'monthly') {
        monthly.push(dbEmp)
      } else {
        // Special cases: -, per space, per video, commission, etc.
        specialCases.push({ csv: csvEmp, db: dbEmp })
      }
    }

    console.log(`📊 Categorization complete:`)
    console.log(`   - Hourly: ${hourly.length}`)
    console.log(`   - Monthly: ${monthly.length}`)
    console.log(`   - Special cases: ${specialCases.length}`)
    console.log(`   - Not found in DB: ${notFound.length}`)

    return { hourly, monthly, specialCases, notFound }
  }

  /**
   * Update employee compensation types in database
   */
  private async updateDatabase(categories: {
    hourly: DatabaseEmployee[]
    monthly: DatabaseEmployee[]
  }): Promise<UpdateResult> {
    console.log('💾 Updating database...')

    const result: UpdateResult = {
      updated: [],
      failed: [],
      specialCases: [],
      alreadyCorrect: []
    }

    // Update to hourly
    for (const emp of categories.hourly) {
      if (emp.rate === 'hourly') {
        result.alreadyCorrect.push(`${emp.full_name} (already hourly)`)
        continue
      }

      try {
        if (!this.dryRun) {
          const { error } = await supabaseAdmin
            .from('appy_employees')
            .update({ rate: 'hourly' })
            .eq('id', emp.id)

          if (error) throw error
        }

        result.updated.push(`${emp.full_name}: ${emp.rate} → hourly`)
      } catch (error) {
        result.failed.push(`${emp.full_name}: Failed to update - ${error}`)
      }
    }

    // Update to monthly
    for (const emp of categories.monthly) {
      if (emp.rate === 'monthly') {
        result.alreadyCorrect.push(`${emp.full_name} (already monthly)`)
        continue
      }

      try {
        if (!this.dryRun) {
          const { error } = await supabaseAdmin
            .from('appy_employees')
            .update({ rate: 'monthly' })
            .eq('id', emp.id)

          if (error) throw error
        }

        result.updated.push(`${emp.full_name}: ${emp.rate} → monthly`)
      } catch (error) {
        result.failed.push(`${emp.full_name}: Failed to update - ${error}`)
      }
    }

    return result
  }

  /**
   * Generate detailed report
   */
  private generateReport(
    categories: ReturnType<typeof this.categorizeEmployees>,
    updateResult: UpdateResult
  ): void {
    console.log('\n📋 DETAILED REPORT')
    console.log('=' .repeat(50))

    if (updateResult.updated.length > 0) {
      console.log(`\n✅ UPDATED (${updateResult.updated.length}):`)
      updateResult.updated.forEach(update => console.log(`   - ${update}`))
    }

    if (updateResult.alreadyCorrect.length > 0) {
      console.log(`\n✓ ALREADY CORRECT (${updateResult.alreadyCorrect.length}):`)
      updateResult.alreadyCorrect.forEach(emp => console.log(`   - ${emp}`))
    }

    if (updateResult.failed.length > 0) {
      console.log(`\n❌ FAILED (${updateResult.failed.length}):`)
      updateResult.failed.forEach(failure => console.log(`   - ${failure}`))
    }

    if (categories.specialCases.length > 0) {
      console.log(`\n⚠️ SPECIAL CASES (${categories.specialCases.length}):`)
      categories.specialCases.forEach(({ csv, db }) => {
        const dbInfo = db ? `DB: ${db.full_name} (${db.rate})` : 'Not found in DB'
        console.log(`   - ${csv.name} (${csv.type}) → ${dbInfo}`)
      })
    }

    if (categories.notFound.length > 0) {
      console.log(`\n🔍 NOT FOUND IN DB (${categories.notFound.length}):`)
      categories.notFound.forEach(emp => console.log(`   - ${emp.name} (${emp.type})`))
    }

    console.log('\n📊 SUMMARY:')
    console.log(`   - Total CSV entries: ${this.csvData.length}`)
    console.log(`   - Total DB employees: ${this.dbEmployees.length}`)
    console.log(`   - Successfully processed: ${updateResult.updated.length + updateResult.alreadyCorrect.length}`)
    console.log(`   - Failed: ${updateResult.failed.length}`)
    console.log(`   - Special cases: ${categories.specialCases.length}`)
    console.log(`   - Not found: ${categories.notFound.length}`)
  }

  /**
   * Main execution function
   */
  async run(): Promise<void> {
    try {
      console.log('🚀 Starting compensation type update process...\n')

      // Step 1: Parse CSV
      this.parseCsv()

      // Step 2: Fetch database employees
      await this.fetchDatabaseEmployees()

      // Step 3: Categorize employees
      const categories = this.categorizeEmployees()

      // Step 4: Update database
      const updateResult = await this.updateDatabase(categories)

      // Step 5: Generate report
      this.generateReport(categories, updateResult)

      console.log(`\n${this.dryRun ? '🧪 DRY RUN COMPLETE' : '✅ UPDATE COMPLETE'}`)
      
      if (this.dryRun) {
        console.log('\n💡 To execute the actual updates, run with --execute flag')
      }

    } catch (error) {
      console.error('❌ Error during execution:', error)
      process.exit(1)
    }
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2)
  const dryRun = !args.includes('--execute')

  const updater = new CompensationTypeUpdater(dryRun)
  await updater.run()
}

if (require.main === module) {
  main()
}

export { CompensationTypeUpdater }