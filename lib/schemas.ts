import { z } from 'zod'

// Base validation helpers
const sanitizeString = (str: string) => {
  if (typeof str !== 'string') {
    console.warn('⚠️ [DEBUG] sanitizeString received non-string value:', typeof str, str)
    return String(str).trim().replace(/[<>]/g, '')
  }
  return str.trim().replace(/[<>]/g, '')
}
const nonEmptyString = z.string().min(1, 'This field is required').transform(sanitizeString)

// User role validation
export const userRoleSchema = z.enum(['super-admin', 'hr-admin', 'senior-manager', 'admin', 'manager', 'accountant'])

// Department validation
export const departmentSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string()
    .min(2, 'Department name must be at least 2 characters')
    .max(50, 'Department name must be less than 50 characters')
    .transform(sanitizeString)
    .refine(name => !/^\s*$/.test(name), 'Department name cannot be empty or whitespace'),
})

export const departmentFormSchema = departmentSchema.omit({ id: true })

// Manager validation
export const managerSchema = z.object({
  id: z.string().min(1, 'Invalid manager ID'), // Accept Clerk user IDs
  fullName: z.string()
    .min(2, 'Manager name must be at least 2 characters')
    .max(100, 'Manager name must be less than 100 characters')
    .transform(sanitizeString),
  email: z.string()
    .email('Invalid email address')
    .transform(sanitizeString),
  role: userRoleSchema,
  departmentId: z.string().uuid('Invalid department selected').optional(),
})

export const managerFormSchema = managerSchema.omit({ id: true })

// Employee validation
export const employeeSchema = z.object({
  id: z.string().uuid().optional(),
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be less than 100 characters')
    .transform(sanitizeString)
    .refine(name => /^[a-zA-Z\s'-]+$/.test(name), 'Name can only contain letters, spaces, hyphens, and apostrophes'),
  email: z.string()
    .email('Invalid email address')
    .transform(sanitizeString),
  role: z.string()
    .max(100, 'Role must be less than 100 characters')
    .transform(sanitizeString)
    .optional(),
  linkedinUrl: z.string()
    .url('Invalid LinkedIn URL')
    .transform(sanitizeString)
    .optional()
    .or(z.literal('')),
  twitterUrl: z.string()
    .url('Invalid Twitter URL')
    .transform(sanitizeString)
    .optional()
    .or(z.literal('')),
  telegramUrl: z.string()
    .url('Invalid Telegram URL')
    .transform(sanitizeString)
    .optional()
    .or(z.literal('')),
  departmentId: z.string().uuid('Invalid department selected'),
  managerId: z.union([z.string().min(1, 'Invalid manager selected'), z.literal("none")]).nullable(),
  rate: z.enum(['hourly', 'monthly'], {
    required_error: 'Payment frequency is required',
    invalid_type_error: 'Payment frequency must be hourly or monthly'
  }),
  paymentNotes: z.string()
    .max(500, 'Payment notes must be less than 500 characters')
    .transform(sanitizeString)
    .optional()
    .or(z.literal('')),
  active: z.boolean().default(true),
})

export const employeeFormSchema = employeeSchema.omit({ id: true }).extend({
  active: z.boolean(), // Make active field required for form
})

// Appraisal Period validation
const appraisalPeriodBaseObject = z.object({
  periodStart: z.string()
    .datetime('Invalid start date format')
    .or(z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')),
  periodEnd: z.string()
    .datetime('Invalid end date format')
    .or(z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')),
  closed: z.boolean().default(false),
})

const appraisalPeriodWithValidation = appraisalPeriodBaseObject.refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  return end > start
}, {
  message: "End date must be after start date",
  path: ["periodEnd"],
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 365
}, {
  message: "Period cannot be longer than 365 days",
  path: ["periodEnd"],
})

export const appraisalPeriodSchema = appraisalPeriodBaseObject.extend({
  id: z.string().uuid().optional(),
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  return end > start
}, {
  message: "End date must be after start date",
  path: ["periodEnd"],
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 365
}, {
  message: "Period cannot be longer than 365 days",
  path: ["periodEnd"],
})

export const appraisalPeriodFormSchema = appraisalPeriodBaseObject.extend({
  closed: z.boolean(), // Make closed field required for form
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  return end > start
}, {
  message: "End date must be after start date",
  path: ["periodEnd"],
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 365
}, {
  message: "Period cannot be longer than 365 days",
  path: ["periodEnd"],
})

// Appraisal validation
export const appraisalSchema = z.object({
  id: z.string().uuid().optional(),
  periodId: z.string().uuid('Invalid period'),
  employeeId: z.string().uuid('Invalid employee'),
  managerId: z.string().min(1, 'Invalid manager'), // Accept Clerk user IDs (not UUIDs)
  q1: z.enum(['below-expectations', 'meets-expectations', 'exceeds-expectations']).nullable(),
  // Legacy fields - optional for data preservation
  q2: z.boolean().optional(),
  q3: z.string()
    .max(500, 'Response must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  q4: z.string()
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString)
    .optional(),
  q5: z.string()
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString)
    .optional(),
  // New combined field
  projectDescription: z.string()
    .max(2000, 'Response must be less than 2000 characters')
    .transform(sanitizeString)
    .optional(),
  status: z.enum(['draft', 'submitted']).default('draft'),
  paymentStatus: z.enum(['ready-to-pay', 'contact-manager', 'do-not-pay', 'other']).nullable().optional(),
  paymentNote: z.string()
    .max(500, 'Note must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  // Legacy fields - optional for data preservation
  keyContributions: z.string()
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString)
    .optional(),
  extraInitiatives: z.string()
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString)
    .optional(),
  performanceLacking: z.string()
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString)
    .optional(),
  // Updated attendance fields
  tookDaysOff: z.boolean().nullable().optional(),
  daysOffDates: z.string()
    .max(200, 'Dates must be less than 200 characters')
    .transform(sanitizeString)
    .optional(),
  disciplineRating: z.number().int().min(1).max(5).nullable().optional(),
  disciplineComment: z.string()
    .max(500, 'Comment must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  // Legacy field - optional for data preservation
  daysOffTaken: z.number().int().min(0).nullable().optional(),
  impactRating: z.number().int().min(1).max(5).nullable().optional(),
  impactComment: z.string()
    .max(500, 'Comment must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  qualityRating: z.number().int().min(1).max(5).nullable().optional(),
  qualityComment: z.string()
    .max(500, 'Comment must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  collaborationRating: z.number().int().min(1).max(5).nullable().optional(),
  collaborationComment: z.string()
    .max(500, 'Comment must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  skillGrowthRating: z.number().int().min(1).max(5).nullable().optional(),
  skillGrowthComment: z.string()
    .max(500, 'Comment must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  readinessPromotion: z.enum(['strong-yes', 'yes-with-reservations', 'no-not-yet']).nullable().optional(),
  readinessComment: z.string()
    .max(1000, 'Comment must be less than 1000 characters')
    .transform(sanitizeString)
    .optional(),
})

// Appraisal submission validation (requires only active UI fields)
export const appraisalSubmissionSchema = appraisalSchema.extend({
  id: z.string().uuid().optional(), // Make id optional for new submissions
  q1: z.enum(['below-expectations', 'meets-expectations', 'exceeds-expectations'], {
    errorMap: () => ({ message: 'Overall performance rating is required' })
  }),
  status: z.literal('submitted'),
  paymentStatus: z.enum(['ready-to-pay', 'contact-manager', 'do-not-pay', 'other'], {
    errorMap: () => ({ message: 'Payment status is required' })
  }),
  // Optional fields that remain in UI
  disciplineRating: z.number().int().min(1).max(5).nullable().optional(),
  impactRating: z.number().int().min(1).max(5).nullable().optional(),
  qualityRating: z.number().int().min(1).max(5).nullable().optional(),
  collaborationRating: z.number().int().min(1).max(5).nullable().optional(),
  skillGrowthRating: z.number().int().min(1).max(5).nullable().optional(),
  readinessPromotion: z.enum(['strong-yes', 'yes-with-reservations', 'no-not-yet']).nullable().optional(),
})

export const appraisalFormSchema = appraisalSchema.omit({ 
  id: true, 
  periodId: true, 
  employeeId: true, 
  managerId: true, 
  status: true 
})

// Search and filter schemas
export const searchSchema = z.object({
  query: z.string()
    .max(100, 'Search query too long')
    .transform(sanitizeString)
    .optional(),
  department: z.string().uuid().optional(),
  status: z.enum(['draft', 'submitted', 'not-started']).optional(),
  sortBy: z.enum(['name', 'department', 'status', 'submittedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
})

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
})

// Export validation functions
export const validateDepartment = (data: unknown) => departmentSchema.parse(data)
export const validateEmployee = (data: unknown) => employeeSchema.parse(data)
export const validateAppraisal = (data: unknown) => appraisalSchema.parse(data)
export const validateAppraisalSubmission = (data: unknown) => appraisalSubmissionSchema.parse(data)
export const validatePeriod = (data: unknown) => appraisalPeriodSchema.parse(data)

// Type exports
export type Department = z.infer<typeof departmentSchema>
export type Employee = z.infer<typeof employeeSchema>
export type AppraisalPeriod = z.infer<typeof appraisalPeriodSchema>
export type Appraisal = z.infer<typeof appraisalSchema>
export type AppraisalSubmission = z.infer<typeof appraisalSubmissionSchema>
export type UserRole = z.infer<typeof userRoleSchema>
export type SearchParams = z.infer<typeof searchSchema>
export type PaginationParams = z.infer<typeof paginationSchema>

// PTO (Paid Time Off) validation schemas
export const ptoRequestTypeSchema = z.enum(['vacation', 'sick', 'personal', 'emergency'])
export const ptoStatusSchema = z.enum(['pending', 'approved', 'rejected', 'cancelled'])

// PTO Balance validation
export const ptoBalanceSchema = z.object({
  id: z.string().uuid().optional(),
  employeeId: z.string().uuid('Invalid employee'),
  year: z.number().int().min(2020).max(2030),
  totalDays: z.number().int().min(0).max(365),
  usedDays: z.number().int().min(0).max(365),
  availableDays: z.number().int().min(0).max(365),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
})

// PTO Request validation
export const ptoRequestSchema = z.object({
  id: z.string().uuid().optional(),
  employeeId: z.string().uuid('Invalid employee'),
  managerId: z.string().min(1, 'Manager is required'), // Clerk user ID
  requestType: ptoRequestTypeSchema,
  startDate: z.string()
    .datetime('Invalid start date format')
    .or(z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')),
  endDate: z.string()
    .datetime('Invalid end date format')
    .or(z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')),
  daysRequested: z.number().int().min(1, 'Must request at least 1 day').max(7, 'Cannot request more than 7 days'),
  reason: z.string()
    .max(500, 'Reason must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  status: ptoStatusSchema.default('pending'),
  approvedBy: z.string().min(1).optional(),
  approvedAt: z.string().datetime().optional(),
  rejectedReason: z.string()
    .max(500, 'Rejection reason must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
}).refine(data => {
  const start = new Date(data.startDate)
  const end = new Date(data.endDate)
  return end >= start
}, {
  message: "End date must be after or equal to start date",
  path: ["endDate"],
}).refine(data => {
  const start = new Date(data.startDate)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return start >= today
}, {
  message: "Start date cannot be in the past",
  path: ["startDate"],
}).refine(data => {
  const start = new Date(data.startDate)
  const end = new Date(data.endDate)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  return diffDays === data.daysRequested
}, {
  message: "Days requested must match the date range",
  path: ["daysRequested"],
})

// PTO Request form validation (for client-side forms)
export const ptoRequestFormSchema = z.object({
  requestType: z.enum(['vacation', 'sick', 'personal', 'emergency']),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  daysRequested: z.number().min(1, 'Must request at least 1 day').max(7, 'Cannot request more than 7 days'),
  reason: z.string().optional(),
}).refine((data) => {
  const startDate = new Date(data.startDate)
  const endDate = new Date(data.endDate)
  return endDate >= startDate
}, {
  message: "End date must be after start date",
  path: ["endDate"],
})

// PTO Approval action validation
export const ptoApprovalSchema = z.object({
  requestId: z.string().uuid('Invalid request ID'),
  action: z.enum(['approve', 'reject'], {
    errorMap: () => ({ message: 'Action must be either approve or reject' })
  }),
  rejectedReason: z.string()
    .max(500, 'Rejection reason must be less than 500 characters')
    .transform(sanitizeString)
    .optional(),
}).refine(data => {
  if (data.action === 'reject') {
    return data.rejectedReason && data.rejectedReason.trim().length > 0
  }
  return true
}, {
  message: "Rejection reason is required when rejecting a request",
  path: ["rejectedReason"],
})

// PTO Statistics validation
export const ptoStatsSchema = z.object({
  totalRequests: z.number().int().min(0),
  pendingRequests: z.number().int().min(0),
  approvedRequests: z.number().int().min(0),
  rejectedRequests: z.number().int().min(0),
})

// Export validation functions
export const validatePTOBalance = (data: unknown) => ptoBalanceSchema.parse(data)
export const validatePTORequest = (data: unknown) => ptoRequestSchema.parse(data)
export const validatePTORequestForm = (data: unknown) => ptoRequestFormSchema.parse(data)
export const validatePTOApproval = (data: unknown) => ptoApprovalSchema.parse(data)
export const validatePTOStats = (data: unknown) => ptoStatsSchema.parse(data)

// Type exports for PTO
export type PTOBalance = z.infer<typeof ptoBalanceSchema>
export type PTORequest = z.infer<typeof ptoRequestSchema>
export type PTORequestForm = z.infer<typeof ptoRequestFormSchema>
export type PTOApproval = z.infer<typeof ptoApprovalSchema>
export type PTOStats = z.infer<typeof ptoStatsSchema>
export type PTORequestType = z.infer<typeof ptoRequestTypeSchema>
export type PTOStatus = z.infer<typeof ptoStatusSchema>

console.log('📋 Validation schemas loaded successfully')
