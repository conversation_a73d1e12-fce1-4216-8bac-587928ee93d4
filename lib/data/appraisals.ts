import type { EmployeeAppraisal, AppraisalDetails, UserRole, AppraisalStatus } from '../types'
import { db } from '../db'
import { getEmployees, getEmployeesForManager, getDirectReportsForManager, getManagerRole, getBatchManagerRoles } from './employees'
import { getPeriods } from './periods'
import { debug } from '../debug'

export async function getManagerAppraisals(periodId?: string): Promise<EmployeeAppraisal[]> {
  try {
    const { getCurrentUser, hasSuperAdminAccess } = await import('../auth')
    const currentUser = await getCurrentUser()

    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }

    debug.log('🔍 [DEBUG] getManagerAppraisals - Current user:', {
      id: currentUser.id,
      fullName: currentUser.fullName,
      role: currentUser.role
    })

    // Super-admins see all employees, senior-managers see hierarchical employees, regular managers see direct reports
    let managedEmployees
    if (hasSuperAdminAccess(currentUser)) {
      const employees = await getEmployees()
      managedEmployees = employees
      debug.log('👥 [DEBUG] getManagerAppraisals - Super admin sees all employees:', employees.length)
    } else if (currentUser.role === 'senior-manager') {
      // Use hierarchical function for senior managers to see their entire hierarchy
      managedEmployees = await getEmployeesForManager(currentUser.id, currentUser.role)
      debug.log('🌳 [DEBUG] getManagerAppraisals - Senior manager sees hierarchical employees:', managedEmployees.length)
    } else if (currentUser.role === 'manager') {
      // Regular managers see only direct reports
      managedEmployees = await getDirectReportsForManager(currentUser.id)
      debug.log('👥 [DEBUG] getManagerAppraisals - Manager sees direct reports:', managedEmployees.length)
    } else {
      // Fallback for other roles
      managedEmployees = []
      debug.log('⚠️ [DEBUG] getManagerAppraisals - No employees for role:', currentUser.role)
    }

    debug.log('🎯 [DEBUG] getManagerAppraisals - Managed employees:', managedEmployees.map(emp => ({
      id: emp.id,
      fullName: emp.fullName,
      managerId: emp.managerId,
      managerName: emp.managerName
    })))

    debug.log(`📊 [DEBUG] getManagerAppraisals - Found ${managedEmployees.length} employees managed by ${currentUser.fullName} (ID: ${currentUser.id})`)

    // Universal manager role detection - batch check managers table for all employees
    debug.log('🔑 [DEBUG] getManagerAppraisals - Using batch universal manager role detection')

    // Batch get manager roles for all employees
    const batchManagerRoles = await getBatchManagerRoles(
      managedEmployees.map(emp => ({ id: emp.id, fullName: emp.fullName }))
    )

    // Create a map of employee ID to role using batch results
    const roleMap = new Map<string, string>()
    
    managedEmployees.forEach(emp => {
      const managerRole = batchManagerRoles.get(emp.id)
      const finalRole = managerRole || (emp as any).role || 'employee'

      debug.log(`🎯 [DEBUG] Employee ${emp.fullName}: manager_role=${managerRole}, employee_role=${(emp as any).role}, final_role=${finalRole}`)
      roleMap.set(emp.id, finalRole)
    })

    debug.log('🗺️ [DEBUG] Final roleMap:', Array.from(roleMap.entries()))
    debug.log('🔍 [DEBUG] Employee names being checked:', managedEmployees.map(emp => emp.fullName))

    // Get the target period (either specified or current active period)
    const periods = await getPeriods()
    let targetPeriod
    
    if (periodId) {
      targetPeriod = periods.find(p => p.id === periodId)
      if (!targetPeriod) {
        console.warn(`Specified period ${periodId} not found`)
        return []
      }
    } else {
      targetPeriod = periods.find(p => !p.closed)
      if (!targetPeriod) {
        console.warn('No active appraisal period found')
        // Return employees with not-started status if no active period
        return managedEmployees.map((emp) => ({
          employeeId: emp.id,
          fullName: emp.fullName,
          departmentName: emp.departmentName!,
          status: "not-started" as const,
          submittedAt: undefined,
          role: (roleMap.get(emp.id) as UserRole) || 'employee',
          isManager: ['manager', 'senior-manager', 'super-admin'].includes(roleMap.get(emp.id) || ''),
          managerName: emp.managerName || undefined,
        }))
      }
    }

    const isCurrentPeriod = !targetPeriod.closed
    debug.log(`📅 [DEBUG] getManagerAppraisals - Using period: ${targetPeriod.id}, is current: ${isCurrentPeriod}`)

    // Get appraisals for the target period
    const allAppraisals = await db.getAppraisalsWithEmployeeData(targetPeriod.id)
    
    // Filter appraisals based on user role
    // Super-admins see ALL appraisals, others see only those they created
    const appraisals = hasSuperAdminAccess(currentUser) 
      ? allAppraisals 
      : allAppraisals.filter(appraisal => appraisal.manager_id === currentUser.id)

    debug.log('📋 [DEBUG] getManagerAppraisals - Fetched appraisals:', appraisals.map(appraisal => ({
      employee_id: appraisal.employee_id,
      manager_id: appraisal.manager_id,
      status: appraisal.status,
      submitted_at: appraisal.submitted_at
    })))

    // Create a map of employee ID to appraisal data (scoped to current manager, or all for super-admins)
    const appraisalMap = new Map(appraisals.map(appraisal => [appraisal.employee_id, appraisal]))

    // Define which statuses to show in active dashboard - include all statuses to show complete picture
    const activeStatuses: AppraisalStatus[] = ['not-started', 'draft', 'submitted', 'rejected', 'senior-needed', 'approved', 'ready-to-pay', 'contact-manager']
    
    // Build the result with real appraisal statuses and filtering
    const results = managedEmployees.map((emp) => {
      const appraisal = appraisalMap.get(emp.id)

      // Map database status to UI status
      let status: AppraisalStatus = 'not-started'
      let submittedAt: string | undefined = undefined

      if (appraisal) {
        debug.log(`📝 [DEBUG] getManagerAppraisals - Employee ${emp.fullName} appraisal status: ${appraisal.status}`)
        
        // Map database statuses to UI statuses
        const dbStatus = appraisal.status as string
        switch (dbStatus) {
          case 'submitted':
            status = 'submitted'
            submittedAt = appraisal.submitted_at || undefined
            break
          case 'pending':
            status = 'draft'
            break
          case 'approved':
            status = 'approved'
            break
          case 'rejected':
            status = 'rejected'
            break
          case 'ready-to-pay':
            status = 'ready-to-pay'
            break
          case 'contact-manager':
            status = 'contact-manager'
            break
          case 'senior-needed':
            status = 'senior-needed'
            break
          default:
            status = 'not-started'
        }
      } else {
        debug.log(`📝 [DEBUG] getManagerAppraisals - No appraisal found for employee ${emp.fullName}`)
      }

      return {
        employeeId: emp.id,
        fullName: emp.fullName,
        departmentName: emp.departmentName!,
        status,
        submittedAt,
        role: (roleMap.get(emp.id) as UserRole) || 'employee',
        isManager: ['manager', 'senior-manager', 'super-admin'].includes(roleMap.get(emp.id) || ''),
        managerName: emp.managerName || undefined,
      }
    })

    // Filter results based on whether we're viewing current or historical period
    if (isCurrentPeriod) {
      // For current period, only show active appraisals (exclude completed ones)
      const filteredResults = results.filter(emp => activeStatuses.includes(emp.status))
      debug.log(`🔍 [DEBUG] getManagerAppraisals - Filtered ${results.length} down to ${filteredResults.length} active appraisals`)
      return filteredResults
    } else {
      // For historical periods, show all appraisals
      debug.log(`📜 [DEBUG] getManagerAppraisals - Showing all ${results.length} historical appraisals`)
      return results
    }
  } catch (error) {
    console.error('Failed to fetch manager appraisals:', error)
    return []
  }
}

export async function getPreviousAppraisal(employeeId: string): Promise<AppraisalDetails | null> {
  try {
    // Get all periods sorted by date
    const periods = await getPeriods()
    const sortedPeriods = periods.sort((a, b) => new Date(b.periodStart).getTime() - new Date(a.periodStart).getTime())
    
    // Find the current period
    const currentPeriod = sortedPeriods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return null
    }
    
    // Find the previous period (the most recent closed period)
    const previousPeriod = sortedPeriods.find(p => p.closed && new Date(p.periodStart) < new Date(currentPeriod.periodStart))
    
    if (!previousPeriod) {
      // console.log('No previous appraisal period found')
      return null
    }
    
    // Get the appraisal for the previous period
    const previousAppraisal = await db.getAppraisalByEmployeeId(employeeId, previousPeriod.id)
    
    if (!previousAppraisal) {
      // console.log('No previous appraisal found for employee')
      return null
    }
    
  return {
      id: previousAppraisal.id,
      periodId: previousAppraisal.period_id,
      employeeId: previousAppraisal.employee_id,
      managerId: previousAppraisal.manager_id,
      q1: previousAppraisal.question_1 as 'below-expectations' | 'meets-expectations' | 'exceeds-expectations' | null,
      q2: previousAppraisal.question_2 === 'true' ? true : previousAppraisal.question_2 === 'false' ? false : false,
      q3: previousAppraisal.question_3 || '',
      q4: previousAppraisal.question_4 || '',
      q5: previousAppraisal.question_5 || '',
      // Newly added fields
      projectDescription: previousAppraisal.project_description || '',
      tookDaysOff: previousAppraisal.took_days_off ?? null,
      daysOffDates: previousAppraisal.days_off_dates || '',
      paymentNote: previousAppraisal.payment_note || '',
      status: previousAppraisal.status as 'draft' | 'submitted',
      paymentStatus: previousAppraisal.payment_status as 'ready-to-pay' | 'contact-manager' | 'do-not-pay' | 'other' | null,
      // New fields
      keyContributions: previousAppraisal.key_contributions || '',
      extraInitiatives: previousAppraisal.extra_initiatives || '',
      performanceLacking: previousAppraisal.performance_lacking || '',
      disciplineRating: previousAppraisal.discipline_rating || null,
      disciplineComment: previousAppraisal.discipline_comment || '',
      daysOffTaken: previousAppraisal.days_off_taken || null,
      impactRating: previousAppraisal.impact_rating || null,
      impactComment: previousAppraisal.impact_comment || '',
      qualityRating: previousAppraisal.quality_rating || null,
      qualityComment: previousAppraisal.quality_comment || '',
      collaborationRating: previousAppraisal.collaboration_rating || null,
      collaborationComment: previousAppraisal.collaboration_comment || '',
      skillGrowthRating: previousAppraisal.skill_growth_rating || null,
      skillGrowthComment: previousAppraisal.skill_growth_comment || '',
      readinessPromotion: previousAppraisal.readiness_promotion as 'strong-yes' | 'yes-with-reservations' | 'no-not-yet' | null,
      readinessComment: previousAppraisal.readiness_comment || ''
    }
  } catch (error) {
    console.error('Failed to fetch previous appraisal:', error)
    return null
  }
}

export async function getAppraisalDetails(employeeId: string, managerId?: string): Promise<AppraisalDetails | null> {
  try {
    // Get the current active period (in real app, this would be more sophisticated)
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return null
    }

    const appraisal = await db.getAppraisalByEmployeeId(employeeId, currentPeriod.id, managerId)

    if (!appraisal) {
      // No current appraisal exists, try to prefill from previous period
      const previousAppraisal = await getPreviousAppraisal(employeeId)
      
      if (previousAppraisal) {
        // Return a new draft appraisal structure prefilled with previous data
        // console.log('Prefilling appraisal with previous month data')
        return {
          id: '', // Will be generated when saved
          periodId: currentPeriod.id,
          employeeId,
          managerId: managerId || '', // Use passed managerId or empty string
          q1: previousAppraisal.q1, // Prefill from previous
          q2: previousAppraisal.q2, // Prefill from previous
          q3: previousAppraisal.q3, // Prefill from previous
          q4: '', // Reset this field as it's month-specific
          q5: '', // Reset this field as it's month-specific
          status: 'draft'
        }
      }
      
      // No previous appraisal found, return empty structure
      return {
        id: '', // Will be generated when saved
        periodId: currentPeriod.id,
        employeeId,
        managerId: managerId || '', // Use passed managerId or empty string
        q1: null,
        q2: false,
        q3: '',
        q4: '',
        q5: '',
        status: 'draft'
      }
    }

  return {
      id: appraisal.id,
      periodId: appraisal.period_id,
      employeeId: appraisal.employee_id,
      managerId: appraisal.manager_id,
      q1: appraisal.question_1 as 'below-expectations' | 'meets-expectations' | 'exceeds-expectations' | null,
      q2: appraisal.question_2 === 'true' ? true : appraisal.question_2 === 'false' ? false : false,
      q3: appraisal.question_3 || '',
      q4: appraisal.question_4 || '',
      q5: appraisal.question_5 || '',
      // Newly added fields
      projectDescription: appraisal.project_description || '',
      tookDaysOff: appraisal.took_days_off ?? null,
      daysOffDates: appraisal.days_off_dates || '',
      paymentNote: appraisal.payment_note || '',
      status: appraisal.status as 'draft' | 'submitted',
      paymentStatus: appraisal.payment_status as 'ready-to-pay' | 'contact-manager' | 'do-not-pay' | 'other' | null,
      revisionNumber: appraisal.revision_number || 1,
      isRevision: appraisal.is_revision || false,
      originalSubmissionDate: appraisal.original_submission_date || undefined,
      lastEditedAt: appraisal.last_edited_at || appraisal.created_at,
      // New fields
      keyContributions: appraisal.key_contributions || '',
      extraInitiatives: appraisal.extra_initiatives || '',
      performanceLacking: appraisal.performance_lacking || '',
      disciplineRating: appraisal.discipline_rating || null,
      disciplineComment: appraisal.discipline_comment || '',
      daysOffTaken: appraisal.days_off_taken || null,
      impactRating: appraisal.impact_rating || null,
      impactComment: appraisal.impact_comment || '',
      qualityRating: appraisal.quality_rating || null,
      qualityComment: appraisal.quality_comment || '',
      collaborationRating: appraisal.collaboration_rating || null,
      collaborationComment: appraisal.collaboration_comment || '',
      skillGrowthRating: appraisal.skill_growth_rating || null,
      skillGrowthComment: appraisal.skill_growth_comment || '',
      readinessPromotion: appraisal.readiness_promotion as 'strong-yes' | 'yes-with-reservations' | 'no-not-yet' | null,
      readinessComment: appraisal.readiness_comment || ''
    }
  } catch (error) {
    console.error('Failed to fetch appraisal details:', error)
    return null
  }
}

// Get pending appraisals for approval based on user role
export async function getPendingApprovals(): Promise<EmployeeAppraisal[]> {
  try {
    const { getCurrentUser, canApproveAppraisals } = await import('../auth')
    const currentUser = await getCurrentUser()

    if (!currentUser || !canApproveAppraisals(currentUser.role)) {
      console.error('User not authorized to view pending approvals')
      return []
    }

    console.log('🔍 [DEBUG] getPendingApprovals - Current user:', {
      id: currentUser.id,
      fullName: currentUser.fullName,
      role: currentUser.role
    })

    // Get the current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)

    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return []
    }

    // Get all submitted appraisals for the current period
    const submittedAppraisals = await db.getSubmittedAppraisals(currentPeriod.id)
    const employees = await getEmployees()
    
    console.log('📋 [DEBUG] getPendingApprovals - Submitted appraisals:', submittedAppraisals.length)

    // Filter appraisals that need approval based on role hierarchy
    const pendingApprovals: EmployeeAppraisal[] = []

    for (const appraisal of submittedAppraisals) {
      const employee = employees.find(emp => emp.id === appraisal.employee_id)
      if (!employee) continue

      // Check if current user can approve this specific employee's appraisal
      const { canApproveEmployeeAppraisal } = await import('../auth')
      const canApprove = await canApproveEmployeeAppraisal(employee.id)

      if (canApprove) {
        pendingApprovals.push({
          employeeId: employee.id,
          fullName: employee.fullName,
          departmentName: employee.departmentName!,
          status: 'submitted' as const,
          submittedAt: appraisal.submitted_at || undefined,
        })
      }
    }

    return pendingApprovals
  } catch (error) {
    console.error('Failed to fetch pending approvals:', error)
    return []
  }
}

// Approve an appraisal
export async function approveAppraisal(appraisalId: string, approvedBy: string): Promise<boolean> {
  try {
    const result = await db.updateAppraisalStatus(appraisalId, 'approved', approvedBy)
    return result
  } catch (error) {
    console.error('Failed to approve appraisal:', error)
    return false
  }
}

// Reject an appraisal
export async function rejectAppraisal(appraisalId: string, rejectedBy: string, reason?: string): Promise<boolean> {
  try {
    const result = await db.updateAppraisalStatus(appraisalId, 'rejected', rejectedBy, reason)
    return result
  } catch (error) {
    console.error('Failed to reject appraisal:', error)
    return false
  }
}
