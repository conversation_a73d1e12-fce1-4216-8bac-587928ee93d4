// Barrel export file for all data access functions
// This maintains backward compatibility while organizing code into smaller modules

// Department exports
export { getDepartments, saveDepartment } from './departments'

// Manager exports
export { getManagers, saveManager } from './managers'

// Employee exports
export { getEmployees, saveEmployee, getEmployeeDetails, getEmployeesForManager } from './employees'

// Period exports
export { getPeriods, savePeriod } from './periods'

// Appraisal exports
export { getManagerAppraisals, getPreviousAppraisal, getAppraisalDetails, getPendingApprovals, approveAppraisal, rejectAppraisal } from './appraisals'

// Approval exports  
export { 
  getPendingApprovalsForUser, 
  getCompletedApprovalsForUser, 
  getApprovalStatistics,
  createApprovalWorkflow,
  processApprovalStep,
  getApprovalWorkflow,
  canUserApproveStep
} from './approvals'

// Accounting exports
export { getAccountingData, getAccountingDataForUser } from './accounting'

// Performance stats exports
export { getPerformanceStats, getManagerPerformanceStats } from './performance-stats'

// PTO exports
export {
  getPTOBalance,
  getPTOBalanceForCurrentUser,
  getPTORequestsForManager,
  getPTORequestsForEmployee,
  getPTODashboardData,
  getPTOStats,
  checkPTOAvailability,
  initializeMarketingDepartmentPTO
} from './pto'

// Cache exports (from parent directory)
export {
  cache,
  invalidatePerformanceStatsCache,
  invalidatePTOCache,
  clearAllCache
} from '../cache'