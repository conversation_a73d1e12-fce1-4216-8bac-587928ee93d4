import type {
  AccountingViewData,
  AccountingStats,
  Employee
} from '../types'
import { db } from '../db'
import { supabaseAdmin } from '../supabase-admin'
import { getEmployees, getEmployeesForManager } from './employees'
import { getPeriods } from './periods'
import { debug } from '../debug'

export async function getAccountingData(): Promise<AccountingViewData[]> {
  try {
    const employees = await getEmployees()
    // TODO: In real implementation, this would join with appraisals table to get actual status and submission times
    return employees.map((emp) => {
      const hours = emp.rate === "hourly" ? 160 : 0 // Standard work hours for hourly

      return {
        employeeId: emp.id,
        employeeName: emp.fullName,
        departmentName: emp.departmentName!,
        managerName: emp.managerName || 'Unassigned',
        status: "not-started" as const, // In real app, this would come from appraisals table
        submittedAt: null, // In real app, this would come from appraisals table
        compensation: emp.rate,
        hours,
        paymentStatus: null, // No payment status without appraisal
        appraisalId: undefined,
        paymentNotes: emp.paymentNotes,
      }
    })
  } catch (error) {
    console.error('Failed to fetch accounting data:', error)
    return []
  }
}

export async function getAccountingDataForUser(): Promise<AccountingViewData[]> {
  return getAccountingDataForUserByPeriod()
}

export async function getAccountingDataForUserByPeriod(month?: number, year?: number): Promise<AccountingViewData[]> {
  try {
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()

    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }

    // Get periods and find the target period
    const periods = await getPeriods()
    let targetPeriod

    if (month && year) {
      // Find period for specific month/year
      targetPeriod = periods.find(p => {
        const periodStart = new Date(p.periodStart)
        return periodStart.getMonth() + 1 === month && periodStart.getFullYear() === year
      })

      if (!targetPeriod) {
        console.warn(`No appraisal period found for ${month}/${year}`)
        return []
      }
    } else {
      // Use current active period
      targetPeriod = periods.find(p => !p.closed)

      if (!targetPeriod) {
        console.warn('No active appraisal period found')
        return []
      }
    }
    
    // Filter based on user role
    let filteredEmployees: Employee[]

    if (currentUser.role === 'super-admin') {
      // Super admins see all employees
      const employees = await getEmployees()
      filteredEmployees = employees
      console.log('🔍 [DEBUG] Accounting - Super admin sees all employees:', employees.length)
    } else if (currentUser.role === 'accountant') {
      // Accountants see all employees (for payroll processing)
      const employees = await getEmployees()
      filteredEmployees = employees
      console.log('🔍 [DEBUG] Accounting - Accountant sees all employees:', employees.length)
    } else if (currentUser.role === 'manager') {
      // Managers see employees under their hierarchical supervision (including sub-managers)
      filteredEmployees = await getEmployeesForManager(currentUser.id, currentUser.role)
      console.log('🌳 [DEBUG] Accounting - Manager sees hierarchical employees:', filteredEmployees.length)
    } else if (currentUser.role === 'hr-admin' || currentUser.role === 'admin') {
      // HR admins and admins see all employees
      const employees = await getEmployees()
      filteredEmployees = employees
      console.log('🔍 [DEBUG] Accounting - HR/Admin sees all employees:', employees.length)
    } else {
      // Default: no access
      filteredEmployees = []
      console.log('🚫 [DEBUG] Accounting - No access for role:', currentUser.role)
    }
    
    // Get appraisals for the target period
    const appraisals = await db.getAppraisalsWithEmployeeData(targetPeriod.id)

    // Create a map of employee ID to all their appraisals
    const employeeAppraisalsMap = new Map<string, any[]>()
    appraisals.forEach(appraisal => {
      const employeeId = appraisal.employee_id
      if (!employeeAppraisalsMap.has(employeeId)) {
        employeeAppraisalsMap.set(employeeId, [])
      }
      employeeAppraisalsMap.get(employeeId)!.push(appraisal)
    })

    // Get approval workflow data for all appraisals
    const { getWorkflowByAppraisalId } = await import('./approvals')
    const workflowPromises = appraisals.map(async (appraisal) => {
      const workflow = await getWorkflowByAppraisalId(appraisal.id)
      return { appraisalId: appraisal.id, workflow }
    })
    const workflowResults = await Promise.all(workflowPromises)
    const workflowMap = new Map(workflowResults.map(result => [result.appraisalId, result.workflow]))

    // Build the accounting view data with real appraisal statuses
    return filteredEmployees.map((emp) => {
      const employeeAppraisals = employeeAppraisalsMap.get(emp.id) || []

      // Initialize default values
      let status: 'not-started' | 'draft' | 'submitted' | 'ready-to-pay' | 'contact-manager' | 'senior-needed' = 'not-started'
      let submittedAt: string | null = null
      let paymentStatus: AccountingViewData['paymentStatus'] = 'not-started'
      let appraisalId: string | undefined = undefined
      let managersToContact: string | undefined = undefined

      if (employeeAppraisals.length > 0) {
        // Analyze all appraisals for this employee
        const blockingManagers: { manager: string, status: string }[] = []
        let hasReadyToPay = false
        let mostRecentAppraisal = employeeAppraisals[0]

        // Sort appraisals by submission date (most recent first)
        const sortedAppraisals = employeeAppraisals.sort((a, b) => 
          new Date(b.submitted_at || b.created_at).getTime() - new Date(a.submitted_at || a.created_at).getTime()
        )

        // Check each appraisal for blocking statuses and ready-to-pay
        for (const appraisal of sortedAppraisals) {
          const workflow = workflowMap.get(appraisal.id)
          const isWorkflowCompleted = workflow?.status === 'completed'
          
          // Find blocking statuses
          if (appraisal.payment_status === 'contact-manager' || appraisal.payment_status === 'do-not-pay') {
            // Get manager name from the appraisal data (it should be included from the join)
            const managerName = appraisal.appy_managers?.full_name || 'Unknown Manager'
            blockingManagers.push({ 
              manager: managerName, 
              status: appraisal.payment_status 
            })
          }
          
          // Check if any appraisal is ready-to-pay and workflow completed
          if (appraisal.payment_status === 'ready-to-pay' && isWorkflowCompleted) {
            hasReadyToPay = true
          }
        }

        // Use most recent appraisal for basic info
        mostRecentAppraisal = sortedAppraisals[0]
        appraisalId = mostRecentAppraisal.id
        submittedAt = mostRecentAppraisal.submitted_at

        console.log(`🔍 [ACCOUNTING] Employee ${emp.fullName}:`)
        console.log(`  - Total appraisals: ${employeeAppraisals.length}`)
        console.log(`  - Blocking managers: ${blockingManagers.length}`)
        console.log(`  - Has ready-to-pay: ${hasReadyToPay}`)

        // Determine final status and managers to contact
        if (blockingManagers.length > 0) {
          // There are blocking statuses - show contact-manager and list managers to contact
          status = 'contact-manager'
          paymentStatus = 'contact-manager'
          managersToContact = blockingManagers
            .map(({ manager, status }) => `${manager} (${status})`)
            .join(', ')
        } else if (hasReadyToPay) {
          // No blocking statuses and has ready-to-pay approvals
          status = 'ready-to-pay'
          paymentStatus = 'ready-to-pay'
        } else {
          // Handle other statuses based on most recent appraisal
          const workflow = workflowMap.get(mostRecentAppraisal.id)
          const isWorkflowCompleted = workflow?.status === 'completed'

          if (mostRecentAppraisal.status === 'submitted' && !isWorkflowCompleted) {
            status = 'senior-needed'
            paymentStatus = 'senior-needed'
          } else if (mostRecentAppraisal.status === 'submitted') {
            status = 'submitted'
            paymentStatus = null
          } else if (mostRecentAppraisal.status === 'pending') {
            status = 'draft'
            paymentStatus = null
          }
        }
      }

      // Calculate work hours for hourly employees
      const hours = emp.rate === "hourly" ? 160 : 0 // Standard work hours for hourly

      console.log(`💰 [DEBUG] Accounting - Employee ${emp.fullName}: ${emp.rate} compensation type`)

      return {
        employeeId: emp.id,
        employeeName: emp.fullName,
        departmentName: emp.departmentName!,
        managerName: emp.managerName || 'Unassigned',
        status,
        submittedAt,
        compensation: emp.rate,
        hours,
        paymentStatus,
        appraisalId,
        managersToContact,
        paymentNotes: emp.paymentNotes,
      }
    })
  } catch (error) {
    console.error('Failed to fetch accounting data for user:', error)
    return []
  }
}

export async function getAccountingStats(): Promise<AccountingStats> {
  return getAccountingStatsByPeriod()
}

export async function getAccountingStatsByPeriod(month?: number, year?: number): Promise<AccountingStats> {
  try {
    console.log('📊 [DEBUG] Calculating accounting statistics...')

    const accountingData = await getAccountingDataForUserByPeriod(month, year)

    const stats = {
      totalEmployees: accountingData.length,
      readyToPay: accountingData.filter(emp => emp.paymentStatus === 'ready-to-pay').length,
      contactManager: accountingData.filter(emp => emp.paymentStatus === 'contact-manager').length,
      seniorNeeded: accountingData.filter(emp => emp.paymentStatus === 'senior-needed' || emp.status === 'senior-needed').length,
      hourlyEmployees: accountingData.filter(emp => emp.compensation === 'hourly').length,
      monthlyEmployees: accountingData.filter(emp => emp.compensation === 'monthly').length,
      submittedAppraisals: accountingData.filter(emp => emp.status === 'submitted' || emp.paymentStatus).length,
      pendingAppraisals: accountingData.filter(emp => emp.status === 'draft' || emp.status === 'not-started').length,
    }

    // Debug logging for payment statuses
    const contactManagerDetails = accountingData.filter(emp => emp.paymentStatus === 'contact-manager')
    const seniorNeededDetails = accountingData.filter(emp => emp.paymentStatus === 'senior-needed' || emp.status === 'senior-needed')

    console.log('📊 [DEBUG] Contact Manager details:', contactManagerDetails.map(emp => ({
      name: emp.employeeName,
      status: emp.status,
      paymentStatus: emp.paymentStatus
    })))

    console.log('📊 [DEBUG] Senior Approval Needed details:', seniorNeededDetails.map(emp => ({
      name: emp.employeeName,
      status: emp.status,
      paymentStatus: emp.paymentStatus
    })))

    console.log('📊 [DEBUG] Accounting stats calculated:', stats)
    return stats
  } catch (error) {
    console.error('Failed to calculate accounting stats:', error)
    return {
      totalEmployees: 0,
      readyToPay: 0,
      contactManager: 0,
      seniorNeeded: 0,
      hourlyEmployees: 0,
      monthlyEmployees: 0,
      submittedAppraisals: 0,
      pendingAppraisals: 0,
    }
  }
}

/**
 * Get enhanced accounting summary statistics
 */
export async function getEnhancedAccountingSummary(periodId?: string): Promise<{ totalAppraisals: number; pendingReview: number; reviewedCount: number; processedCount: number } | null> {
  try {
    debug.log('📊 [ACCOUNTING] Fetching enhanced accounting summary')

    const { data, error } = await supabaseAdmin
      .rpc('get_accounting_summary', {
        p_period_id: periodId || null
      })
      .single()

    if (error) {
      console.error('❌ [ACCOUNTING] Failed to fetch summary:', error)
      return null
    }

    // Type the response data properly
    const summaryData = data as {
      total_appraisals: number
      pending_review: number
      reviewed_count: number
      processed_count: number
    } | null

    if (!summaryData) {
      return null
    }

    return {
      totalAppraisals: summaryData.total_appraisals || 0,
      pendingReview: summaryData.pending_review || 0,
      reviewedCount: summaryData.reviewed_count || 0,
      processedCount: summaryData.processed_count || 0
    }

  } catch (error) {
    console.error('🚨 [ACCOUNTING] Error fetching summary:', error)
    return null
  }
}

/**
 * Get appraisals needing accounting review
 */
export async function getAppraisalsForAccountingReview(): Promise<{ appraisalId: string; employeeName: string; departmentName: string; managerName: string; submittedDate: string; paymentStatus: 'ready-to-pay' | 'contact-manager' | null; daysPending: number }[]> {
  try {
    debug.log('📋 [ACCOUNTING] Fetching appraisals for review')

    const { data, error } = await supabaseAdmin
      .rpc('get_appraisals_for_accounting_review')

    if (error) {
      console.error('❌ [ACCOUNTING] Failed to fetch appraisals for review:', error)
      return []
    }

    // Define the shape of the data returned from the RPC
    interface AppraisalReviewData {
      appraisal_id: string
      employee_name: string
      department_name: string
      manager_name: string
      submitted_date: string
      payment_status: 'ready-to-pay' | 'contact-manager' | null
      days_pending: number
    }

    return ((data || []) as AppraisalReviewData[]).map((item) => ({
      appraisalId: item.appraisal_id,
      employeeName: item.employee_name,
      departmentName: item.department_name,
      managerName: item.manager_name,
      submittedDate: item.submitted_date,
      paymentStatus: item.payment_status,
      daysPending: item.days_pending
    }))

  } catch (error) {
    console.error('🚨 [ACCOUNTING] Error fetching appraisals for review:', error)
    return []
  }
}

/**
 * Add accounting comment to an appraisal
 */
export async function addAccountingComment(
  appraisalId: string,
  commenterId: string,
  comment: string,
  commentType: 'general' | 'urgent' | 'question' = 'general',
  priority: 'low' | 'normal' | 'high' | 'critical' = 'normal',
  isInternal: boolean = true
): Promise<{ success: boolean; error?: string }> {
  try {
    debug.log('💬 [ACCOUNTING] Adding accounting comment:', appraisalId)

    const { error } = await supabaseAdmin
      .from('appy_accounting_comments')
      .insert({
        appraisal_id: appraisalId,
        commenter_id: commenterId,
        comment,
        comment_type: commentType,
        priority,
        is_internal: isInternal
      })

    if (error) {
      console.error('❌ [ACCOUNTING] Failed to add comment:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [ACCOUNTING] Comment added successfully')
    return { success: true }

  } catch (error) {
    console.error('🚨 [ACCOUNTING] Error adding comment:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
