"use server"

import { revalidatePath } from "next/cache"
import { requirePermission, validateSession } from "../auth"
import { handleServerActionError, ValidationError, AuthorizationError } from "./shared"

export async function bulkUpdateRateType(
  employeeIds: string[],
  rateType: "hourly" | "monthly"
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Validate session and permissions
    const session = await validateSession()
    
    // Only HR admins and super admins can perform bulk operations
    if (session.role !== 'hr-admin' && session.role !== 'super-admin') {
      throw new AuthorizationError('Only HR admins can perform bulk employee updates')
    }

    if (!employeeIds || employeeIds.length === 0) {
      throw new ValidationError("No employees selected")
    }

    if (employeeIds.length > 50) {
      throw new ValidationError("Too many employees selected (max 50)")
    }

    if (!rateType || !["hourly", "monthly"].includes(rateType)) {
      throw new ValidationError("Invalid rate type specified")
    }

    console.log(`🔄 [BULK RATE] Starting bulk rate type update to ${rateType} for ${employeeIds.length} employees`)

    // Import shared admin Supabase client
    const { supabaseAdmin } = await import('../supabase-admin')

    // Update the rate field for all selected employees
    const { data, error } = await supabaseAdmin
      .from('appy_employees')
      .update({ 
        rate: rateType,
        updated_at: new Date().toISOString()
      })
      .in('id', employeeIds)
      .select('id, full_name')

    if (error) {
      console.error('❌ [BULK RATE] Database error:', error)
      throw new Error(`Database error: ${error.message}`)
    }

    const updatedCount = data?.length || 0
    
    console.log(`✅ [BULK RATE] Successfully updated ${updatedCount} employees to ${rateType}`)

    // Log audit entry
    try {
      await supabaseAdmin
        .from('appy_audit_log')
        .insert({
          user_id: session.userId,
          action: 'bulk_update_rate_type',
          table_name: 'appy_employees',
          record_ids: employeeIds,
          changes: {
            rate_type: rateType,
            affected_count: updatedCount
          },
          ip_address: null, // Will be populated by RLS if available
          user_agent: null
        })
    } catch (auditError) {
      console.warn('⚠️ [BULK RATE] Failed to log audit entry:', auditError)
      // Don't fail the main operation if audit logging fails
    }

    // Clear employee cache to ensure UI shows updated data
    const { cache } = await import('../cache')
    cache.delete('all-employees')
    
    // Revalidate relevant pages
    revalidatePath("/dashboard/employees")
    revalidatePath("/dashboard")

    return {
      success: true,
      message: `Successfully updated ${updatedCount} employee${updatedCount === 1 ? '' : 's'} to ${rateType} rate`
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function bulkAssignManagers(
  employeeIds: string[],
  managerIds: string[],
  primaryManagerId?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Validate session and permissions
    const session = await validateSession()
    
    // Only HR admins and super admins can perform bulk operations
    if (session.role !== 'hr-admin' && session.role !== 'super-admin') {
      throw new AuthorizationError('Only HR admins can perform bulk manager assignments')
    }

    if (!employeeIds || employeeIds.length === 0) {
      throw new ValidationError("No employees selected")
    }

    if (!managerIds || managerIds.length === 0) {
      throw new ValidationError("No managers selected")
    }

    if (employeeIds.length > 50) {
      throw new ValidationError("Too many employees selected (max 50)")
    }

    console.log(`🔄 [BULK MANAGERS] Starting bulk manager assignment for ${employeeIds.length} employees`)

    // Import shared admin Supabase client
    const { supabaseAdmin } = await import('../supabase-admin')

    let successCount = 0
    const errors: string[] = []

    for (const employeeId of employeeIds) {
      try {
        // First, remove existing manager assignments for this employee
        await supabaseAdmin
          .from('appy_employee_managers')
          .delete()
          .eq('employee_id', employeeId)

        // Set legacy manager_id to primary manager for backward compatibility
        const legacyManagerId = primaryManagerId || managerIds[0]
        await supabaseAdmin
          .from('appy_employees')
          .update({ 
            manager_id: legacyManagerId,
            updated_at: new Date().toISOString()
          })
          .eq('id', employeeId)

        // Add new manager assignments
        const managerAssignments = managerIds.map((managerId, index) => ({
          employee_id: employeeId,
          manager_id: managerId,
          is_primary: managerId === (primaryManagerId || managerIds[0]),
          assigned_at: new Date().toISOString()
        }))

        const { error: assignmentError } = await supabaseAdmin
          .from('appy_employee_managers')
          .insert(managerAssignments)

        if (assignmentError) {
          console.error(`❌ [BULK MANAGERS] Error assigning managers for ${employeeId}:`, assignmentError)
          errors.push(`Failed to assign managers to employee ${employeeId}: ${assignmentError.message}`)
        } else {
          successCount++
          console.log(`✅ [BULK MANAGERS] Successfully assigned ${managerIds.length} managers to ${employeeId}`)
        }
      } catch (error) {
        console.error(`❌ [BULK MANAGERS] Error processing ${employeeId}:`, error)
        errors.push(`Failed to assign managers to employee ${employeeId}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    console.log(`📊 [BULK MANAGERS] Bulk assignment completed. Success: ${successCount}, Errors: ${errors.length}`)

    // Log audit entry
    try {
      await supabaseAdmin
        .from('appy_audit_log')
        .insert({
          user_id: session.userId,
          action: 'bulk_assign_managers',
          table_name: 'appy_employee_managers',
          record_ids: employeeIds,
          changes: {
            manager_ids: managerIds,
            primary_manager_id: primaryManagerId,
            affected_count: successCount,
            error_count: errors.length
          },
          ip_address: null,
          user_agent: null
        })
    } catch (auditError) {
      console.warn('⚠️ [BULK MANAGERS] Failed to log audit entry:', auditError)
    }

    // Clear employee cache to ensure UI shows updated data
    const { cache } = await import('../cache')
    cache.delete('all-employees')
    
    // Revalidate relevant pages
    revalidatePath("/dashboard/employees")
    revalidatePath("/dashboard")

    if (errors.length > 0) {
      return {
        success: successCount > 0,
        message: `${successCount} assignments completed successfully, ${errors.length} errors occurred`,
        error: errors.join('; ')
      }
    }

    return {
      success: true,
      message: `Successfully assigned managers to ${successCount} employee${successCount === 1 ? '' : 's'}`
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}