import { getEmployees, getDepartments, getManagers } from "@/lib/data/index"
import { getCurrentUser } from "@/lib/auth"
import { EmployeesTable } from "@/components/employees-table"
import { RoleGuard } from "@/components/role-guard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield } from "lucide-react"

export default async function EmployeesPage() {
  const user = await getCurrentUser()

  if (!user) {
    return (
      <Alert variant="destructive">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Authentication required. Please sign in to access this page.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <RoleGuard
      allowedRoles={['manager', 'senior-manager', 'hr-admin', 'admin', 'super-admin']}
      userRole={user.role}
      redirectTo="/dashboard"
    >
      <EmployeeManagementContent user={user} />
    </RoleGuard>
  )
}

async function EmployeeManagementContent({ user }: { user: Awaited<ReturnType<typeof getCurrentUser>> }) {
  const employees = await getEmployees()
  const departments = await getDepartments()
  const managers = await getManagers()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Employee Management</h1>
        <p className="text-sm sm:text-base text-muted-foreground mt-1">Add, edit, and manage all company employees.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle className="text-base sm:text-lg">All Employees</CardTitle>
          <CardDescription className="text-sm">A list of all employees in the system.</CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <EmployeesTable data={employees} departments={departments} managers={managers} user={user} />
        </CardContent>
      </Card>
    </div>
  )
}
