import { getAppraisalDetails, getEmployeeDetails } from "@/lib/data/index"
import { getCurrentUser } from "@/lib/auth"
import { notFound, redirect } from "next/navigation"
import { AppraisalForm } from "@/components/appraisal-form"
import { getCurrentMonthYear, parseMonthYear, formatPeriodName } from "@/lib/utils/period-helpers"

export default async function AppraisalPage({ params }: { params: Promise<{ employeeId: string }> }) {
  const { employeeId } = await params
  
  // Get current user to pass as manager
  const currentUser = await getCurrentUser()
  if (!currentUser) {
    redirect('/sign-in')
  }
  
  const employee = await getEmployeeDetails(employeeId)
  const appraisal = await getAppraisalDetails(employeeId, currentUser.id)

  if (!employee) {
    notFound()
  }

  // Get current period information
  const currentMonthYear = getCurrentMonthYear()
  const { month, year } = parseMonthYear(currentMonthYear)
  const currentPeriodName = formatPeriodName(month, year)

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Appraisal for {employee.fullName}</h1>
        <p className="text-muted-foreground">{employee.departmentName} - {currentPeriodName} Period</p>
      </div>
      <AppraisalForm 
        employee={employee} 
        appraisal={appraisal}
        periodMonth={month}
        periodYear={year}
      />
    </div>
  )
}
