import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { 
  getPendingApprovalsForUser, 
  getCompletedApprovalsForUser,
  getApprovalStatistics 
} from '@/lib/data'

/**
 * GET - Get approvals for current user with optional period filtering
 */
export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has approval permissions
    if (!['manager', 'senior-manager', 'hr-admin', 'admin', 'super-admin'].includes(currentUser.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get all approvals (period filtering can be added later)
    const [pendingApprovals, completedApprovals] = await Promise.all([
      getPendingApprovalsForUser(currentUser.id, currentUser.email),
      getCompletedApprovalsForUser(currentUser.id, currentUser.email)
    ])

    // Get statistics (always for all periods for now)
    const statistics = await getApprovalStatistics(currentUser.id)

    return NextResponse.json({ 
      success: true, 
      data: {
        pendingApprovals,
        completedApprovals,
        statistics
      }
    })

  } catch (error) {
    console.error('Error fetching approvals:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch approvals' 
    }, { status: 500 })
  }
}